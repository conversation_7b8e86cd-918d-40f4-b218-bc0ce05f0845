<template>
  <footer class="app-footer">
    <div class="container">
      <!-- 主要内容区 -->
      <div class="footer-content">
        <!-- 品牌信息 -->
        <div class="footer-section brand-section">
          <div class="brand-info">
            <img src="/logo.svg" alt="Materials Studio答疑平台" class="footer-logo" />
            <h3 class="brand-name">Materials Studio答疑平台</h3>
          </div>
          <p class="brand-description">
            专业的分子模拟学习与问答社区，为研究生、博士生提供高质量的技术支持和学习资源。
          </p>
          <div class="social-links">
            <a href="#" class="social-link" title="微信公众号">
              <el-icon><ChatDotRound /></el-icon>
            </a>
            <a href="#" class="social-link" title="QQ群">
              <el-icon><Message /></el-icon>
            </a>
            <a href="#" class="social-link" title="微博">
              <el-icon><Share /></el-icon>
            </a>
            <a href="#" class="social-link" title="邮箱">
              <el-icon><Message /></el-icon>
            </a>
          </div>
        </div>

        <!-- 快速链接 -->
        <div class="footer-section">
          <h4 class="section-title">快速链接</h4>
          <ul class="link-list">
            <li><router-link to="/questions">问题广场</router-link></li>
            <li><router-link to="/resources">资源库</router-link></li>
            <li><router-link to="/membership">会员中心</router-link></li>
            <li><router-link to="/ask">我要提问</router-link></li>
            <li><router-link to="/help">帮助中心</router-link></li>
          </ul>
        </div>

        <!-- 学习资源 -->
        <div class="footer-section">
          <h4 class="section-title">学习资源</h4>
          <ul class="link-list">
            <li><a href="#">Materials Studio教程</a></li>
            <li><a href="#">分子动力学基础</a></li>
            <li><a href="#">DFT计算指南</a></li>
            <li><a href="#">案例分析</a></li>
            <li><a href="#">常见问题FAQ</a></li>
          </ul>
        </div>

        <!-- 关于我们 -->
        <div class="footer-section">
          <h4 class="section-title">关于我们</h4>
          <ul class="link-list">
            <li><router-link to="/about">平台介绍</router-link></li>
            <li><a href="#">团队介绍</a></li>
            <li><a href="#">联系我们</a></li>
            <li><a href="#">合作伙伴</a></li>
            <li><a href="#">招聘信息</a></li>
          </ul>
        </div>

        <!-- 联系信息 -->
        <div class="footer-section">
          <h4 class="section-title">联系我们</h4>
          <div class="contact-info">
            <div class="contact-item">
              <el-icon><Message /></el-icon>
              <span><EMAIL></span>
            </div>
            <div class="contact-item">
              <el-icon><Phone /></el-icon>
              <span>************</span>
            </div>
            <div class="contact-item">
              <el-icon><Location /></el-icon>
              <span>北京市海淀区中关村大街</span>
            </div>
            <div class="contact-item">
              <el-icon><Clock /></el-icon>
              <span>工作日 9:00-18:00</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 分割线 -->
      <div class="footer-divider"></div>

      <!-- 底部信息 -->
      <div class="footer-bottom">
        <div class="copyright">
          <p>&copy; 2024 Materials Studio答疑平台. 保留所有权利.</p>
        </div>
        <div class="legal-links">
          <a href="#">隐私政策</a>
          <a href="#">服务条款</a>
          <a href="#">Cookie政策</a>
          <a href="#">网站地图</a>
        </div>
        <div class="certifications">
          <span class="cert-item">ICP备案号：京ICP备12345678号</span>
          <span class="cert-item">公安备案号：11010802012345</span>
        </div>
      </div>
    </div>

    <!-- 回到顶部按钮 -->
    <transition name="fade">
      <el-button
        v-show="showBackToTop"
        class="back-to-top"
        circle
        size="large"
        @click="scrollToTop"
      >
        <el-icon><ArrowUp /></el-icon>
      </el-button>
    </transition>
  </footer>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import {
  ChatDotRound,
  Message,
  Share,
  Phone,
  Location,
  Clock,
  ArrowUp
} from '@element-plus/icons-vue'

const showBackToTop = ref(false)

// 滚动监听
const handleScroll = () => {
  showBackToTop.value = window.scrollY > 300
}

// 回到顶部
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

onMounted(() => {
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style lang="scss" scoped>
.app-footer {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: var(--text-white);
  position: relative;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr;
  gap: 48px;
  padding: 60px 0 40px;
  
  @media (max-width: 1024px) {
    grid-template-columns: repeat(3, 1fr);
    gap: 32px;
  }
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 32px;
    text-align: center;
  }
}

.footer-section {
  .section-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--text-white);
  }
}

// 品牌区域
.brand-section {
  .brand-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    
    @media (max-width: 768px) {
      justify-content: center;
    }
  }
  
  .footer-logo {
    width: 40px;
    height: 40px;
  }
  
  .brand-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-white);
    margin: 0;
  }
  
  .brand-description {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 24px;
  }
}

// 社交链接
.social-links {
  display: flex;
  gap: 12px;
  
  @media (max-width: 768px) {
    justify-content: center;
  }
  
  .social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-full);
    color: var(--text-white);
    text-decoration: none;
    transition: all var(--transition-fast);
    
    &:hover {
      background: var(--primary-color);
      transform: translateY(-2px);
    }
    
    .el-icon {
      font-size: 18px;
    }
  }
}

// 链接列表
.link-list {
  list-style: none;
  
  li {
    margin-bottom: 12px;
    
    a {
      color: rgba(255, 255, 255, 0.8);
      text-decoration: none;
      transition: color var(--transition-fast);
      
      &:hover {
        color: var(--text-white);
      }
    }
  }
}

// 联系信息
.contact-info {
  .contact-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    color: rgba(255, 255, 255, 0.8);
    
    @media (max-width: 768px) {
      justify-content: center;
    }
    
    .el-icon {
      font-size: 16px;
      color: var(--primary-light);
    }
  }
}

// 分割线
.footer-divider {
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
  margin: 40px 0;
}

// 底部信息
.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 40px;
  flex-wrap: wrap;
  gap: 24px;
  
  @media (max-width: 768px) {
    flex-direction: column;
    text-align: center;
  }
  
  .copyright {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.875rem;
  }
  
  .legal-links {
    display: flex;
    gap: 24px;
    
    @media (max-width: 768px) {
      flex-wrap: wrap;
      justify-content: center;
    }
    
    a {
      color: rgba(255, 255, 255, 0.6);
      text-decoration: none;
      font-size: 0.875rem;
      transition: color var(--transition-fast);
      
      &:hover {
        color: var(--text-white);
      }
    }
  }
  
  .certifications {
    display: flex;
    flex-direction: column;
    gap: 4px;
    
    @media (max-width: 768px) {
      align-items: center;
    }
    
    .cert-item {
      color: rgba(255, 255, 255, 0.5);
      font-size: 0.75rem;
    }
  }
}

// 回到顶部按钮
.back-to-top {
  position: fixed;
  bottom: 32px;
  right: 32px;
  z-index: 999;
  background: var(--primary-color);
  border: none;
  box-shadow: var(--shadow-lg);
  
  &:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
  }
  
  .el-icon {
    color: var(--text-white);
    font-size: 20px;
  }
}

// 动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--transition-normal);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
