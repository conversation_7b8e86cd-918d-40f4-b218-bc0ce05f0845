# Materials Studio答疑平台

一个专业的Materials Studio软件学习与问答平台，为研究生、博士生等学术人员提供高质量的技术支持和学习资源。

## 项目特色

- 🎯 **专业垂直** - 专注Materials Studio及分子模拟领域
- 👥 **分级会员** - 基础用户、初级会员、高级会员权限体系
- 🎥 **多媒体答疑** - 支持文字、图片、视频等多种形式
- 🔬 **科技感设计** - 高级感界面，适合学术研究人员
- 📱 **响应式设计** - 完美适配桌面、平板、手机设备

## 功能模块

### 用户系统
- 用户注册/登录（支持邮箱、手机验证）
- 个人资料管理
- 会员等级管理
- 积分与激励体系

### 问答系统
- 问题发布与分类
- 多媒体回答（文字、图片、视频）
- 评论与互动
- 智能搜索与推荐
- 权限控制（基础/会员专享内容）

### 会员系统
- 多种会员套餐
- 在线支付
- 权限控制
- 专家一对一咨询

### 资源库
- Materials Studio模板下载
- 视频教程
- 文档资料
- 案例分析

### 后台管理
- 内容审核
- 用户管理
- 数据统计
- 系统设置

## 技术架构

### 前端技术栈
- **框架**: Vue.js 3
- **UI库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router
- **样式**: SCSS
- **动画**: GSAP
- **构建工具**: Vite

### 后端技术栈
- **运行环境**: Node.js
- **框架**: Express.js
- **数据库**: MySQL 8.0
- **缓存**: Redis 6.0
- **认证**: JWT
- **文件存储**: 本地存储/阿里云OSS

### 部署方案
- **容器化**: Docker + Docker Compose
- **Web服务器**: Nginx
- **HTTPS**: Let's Encrypt
- **监控**: Prometheus + Grafana

## 快速开始

### 环境要求
- Node.js 16+
- Docker & Docker Compose
- MySQL 8.0
- Redis 6.0

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd materials-studio-platform
```

2. **使用Docker启动（推荐）**
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps
```

3. **手动安装**
```bash
# 安装后端依赖
cd backend
npm install

# 安装前端依赖
cd ../frontend
npm install

# 启动后端服务
cd ../backend
npm start

# 启动前端服务
cd ../frontend
npm run serve
```

### 访问地址
- 前端应用: http://localhost:3000
- 后端API: http://localhost:3001
- 管理后台: http://localhost:3000/admin

### 默认账号
- 管理员: admin / Admin@123
- 测试用户: test / Test@123

## 项目结构

```
materials-studio-platform/
├── frontend/                 # 前端Vue.js应用
│   ├── src/
│   │   ├── components/      # 公共组件
│   │   ├── views/          # 页面组件
│   │   ├── store/          # Pinia状态管理
│   │   ├── router/         # 路由配置
│   │   ├── api/            # API接口
│   │   ├── utils/          # 工具函数
│   │   └── assets/         # 静态资源
│   ├── public/             # 公共文件
│   └── package.json
├── backend/                  # 后端Node.js应用
│   ├── src/
│   │   ├── controllers/    # 控制器
│   │   ├── models/         # 数据模型
│   │   ├── routes/         # 路由定义
│   │   ├── middleware/     # 中间件
│   │   ├── services/       # 业务逻辑
│   │   └── utils/          # 工具函数
│   ├── config/             # 配置文件
│   └── package.json
├── database/                 # 数据库相关
│   ├── init.sql            # 初始化脚本
│   └── migrations/         # 数据库迁移
├── docker-compose.yml        # Docker编排文件
├── nginx.conf               # Nginx配置
└── README.md
```

## 开发指南

### 前端开发
```bash
cd frontend
npm run serve    # 开发模式
npm run build    # 生产构建
npm run lint     # 代码检查
```

### 后端开发
```bash
cd backend
npm run dev      # 开发模式（nodemon）
npm start        # 生产模式
npm test         # 运行测试
```

### 数据库管理
```bash
# 进入MySQL容器
docker exec -it mysql mysql -u root -p

# 备份数据库
docker exec mysql mysqldump -u root -p materials_studio_qa > backup.sql

# 恢复数据库
cat backup.sql | docker exec -i mysql mysql -u root -p materials_studio_qa
```

## API文档

详细的API文档请参考：[API Documentation](./docs/api.md)

主要API端点：
- `POST /api/users/register` - 用户注册
- `POST /api/users/login` - 用户登录
- `GET /api/questions` - 获取问题列表
- `POST /api/questions` - 发布问题
- `GET /api/resources` - 获取资源列表
- `POST /api/memberships/orders` - 创建会员订单

## 部署指南

### 生产环境部署

1. **服务器准备**
```bash
# 安装Docker
curl -fsSL https://get.docker.com | sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑.env文件，设置生产环境配置
```

3. **启动服务**
```bash
docker-compose -f docker-compose.prod.yml up -d
```

4. **配置HTTPS**
```bash
# 使用Let's Encrypt获取SSL证书
sudo certbot --nginx -d yourdomain.com
```

### 监控与维护

- **日志查看**: `docker-compose logs -f`
- **服务状态**: `docker-compose ps`
- **数据备份**: 定时执行备份脚本
- **性能监控**: Grafana面板监控

## 测试

### 单元测试
```bash
# 后端测试
cd backend && npm test

# 前端测试
cd frontend && npm run test:unit
```

### 端到端测试
```bash
# 使用Cypress进行E2E测试
cd frontend && npm run test:e2e
```

### API测试
```bash
# 使用Postman或Newman
newman run tests/api-tests.postman_collection.json
```

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目维护者: [Your Name]
- 邮箱: <EMAIL>
- 项目链接: [https://github.com/yourusername/materials-studio-platform](https://github.com/yourusername/materials-studio-platform)

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 基础用户管理功能
- 问答系统核心功能
- 会员体系实现
- 资源库功能

---

**Materials Studio答疑平台** - 让分子模拟学习更简单！
