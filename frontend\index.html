<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Materials Studio答疑平台 - 专业的分子模拟学习与问答社区" />
    <meta name="keywords" content="Materials Studio, 分子模拟, 问答平台, 学术交流" />
    <title>Materials Studio答疑平台</title>
    
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- 字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- 全局样式 -->
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
      }
      
      #app {
        min-height: 100vh;
      }
      
      /* 加载动画 */
      .loading-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s ease-in-out infinite;
      }
      
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
      
      .loading-text {
        color: white;
        margin-top: 20px;
        font-size: 16px;
        font-weight: 500;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <!-- 初始加载动画 -->
      <div class="loading-container" id="initial-loading">
        <div>
          <div class="loading-spinner"></div>
          <div class="loading-text">Materials Studio答疑平台加载中...</div>
        </div>
      </div>
    </div>
    
    <script type="module" src="/src/main.js"></script>
    
    <!-- 移除初始加载动画 -->
    <script>
      window.addEventListener('load', () => {
        setTimeout(() => {
          const loading = document.getElementById('initial-loading');
          if (loading) {
            loading.style.opacity = '0';
            setTimeout(() => loading.remove(), 300);
          }
        }, 1000);
      });
    </script>
  </body>
</html>
