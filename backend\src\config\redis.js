const Redis = require('redis')
const config = require('./index')
const logger = require('../utils/logger')

// 创建Redis客户端
const redisClient = Redis.createClient({
  host: config.redis.host,
  port: config.redis.port,
  password: config.redis.password,
  db: config.redis.db,
  keyPrefix: config.redis.keyPrefix,
  retryDelayOnFailover: config.redis.retryDelayOnFailover,
  maxRetriesPerRequest: config.redis.maxRetriesPerRequest,
  lazyConnect: true
})

// 错误处理
redisClient.on('error', (error) => {
  logger.error('Redis connection error:', error)
})

redisClient.on('connect', () => {
  logger.info('Redis connected successfully')
})

redisClient.on('ready', () => {
  logger.info('Redis ready to use')
})

redisClient.on('end', () => {
  logger.info('Redis connection ended')
})

// 连接Redis
const connectRedis = async () => {
  try {
    await redisClient.connect()
    return redisClient
  } catch (error) {
    logger.error('Failed to connect to Redis:', error)
    throw error
  }
}

// 关闭Redis连接
const closeRedis = async () => {
  try {
    await redisClient.quit()
    logger.info('Redis connection closed')
  } catch (error) {
    logger.error('Error closing Redis connection:', error)
    throw error
  }
}

// Redis工具方法
const redisUtils = {
  // 设置缓存
  async set(key, value, ttl = config.cache.defaultTTL) {
    try {
      const serializedValue = JSON.stringify(value)
      if (ttl) {
        await redisClient.setEx(key, ttl, serializedValue)
      } else {
        await redisClient.set(key, serializedValue)
      }
      return true
    } catch (error) {
      logger.error('Redis set error:', error)
      return false
    }
  },

  // 获取缓存
  async get(key) {
    try {
      const value = await redisClient.get(key)
      return value ? JSON.parse(value) : null
    } catch (error) {
      logger.error('Redis get error:', error)
      return null
    }
  },

  // 删除缓存
  async del(key) {
    try {
      await redisClient.del(key)
      return true
    } catch (error) {
      logger.error('Redis del error:', error)
      return false
    }
  },

  // 检查键是否存在
  async exists(key) {
    try {
      const result = await redisClient.exists(key)
      return result === 1
    } catch (error) {
      logger.error('Redis exists error:', error)
      return false
    }
  },

  // 设置过期时间
  async expire(key, ttl) {
    try {
      await redisClient.expire(key, ttl)
      return true
    } catch (error) {
      logger.error('Redis expire error:', error)
      return false
    }
  },

  // 获取剩余过期时间
  async ttl(key) {
    try {
      return await redisClient.ttl(key)
    } catch (error) {
      logger.error('Redis ttl error:', error)
      return -1
    }
  },

  // 原子递增
  async incr(key) {
    try {
      return await redisClient.incr(key)
    } catch (error) {
      logger.error('Redis incr error:', error)
      return 0
    }
  },

  // 原子递减
  async decr(key) {
    try {
      return await redisClient.decr(key)
    } catch (error) {
      logger.error('Redis decr error:', error)
      return 0
    }
  },

  // 列表操作
  async lpush(key, ...values) {
    try {
      return await redisClient.lPush(key, values.map(v => JSON.stringify(v)))
    } catch (error) {
      logger.error('Redis lpush error:', error)
      return 0
    }
  },

  async rpop(key) {
    try {
      const value = await redisClient.rPop(key)
      return value ? JSON.parse(value) : null
    } catch (error) {
      logger.error('Redis rpop error:', error)
      return null
    }
  },

  async lrange(key, start, stop) {
    try {
      const values = await redisClient.lRange(key, start, stop)
      return values.map(v => JSON.parse(v))
    } catch (error) {
      logger.error('Redis lrange error:', error)
      return []
    }
  },

  // 集合操作
  async sadd(key, ...members) {
    try {
      return await redisClient.sAdd(key, members.map(m => JSON.stringify(m)))
    } catch (error) {
      logger.error('Redis sadd error:', error)
      return 0
    }
  },

  async smembers(key) {
    try {
      const members = await redisClient.sMembers(key)
      return members.map(m => JSON.parse(m))
    } catch (error) {
      logger.error('Redis smembers error:', error)
      return []
    }
  },

  async sismember(key, member) {
    try {
      return await redisClient.sIsMember(key, JSON.stringify(member))
    } catch (error) {
      logger.error('Redis sismember error:', error)
      return false
    }
  },

  // 有序集合操作
  async zadd(key, score, member) {
    try {
      return await redisClient.zAdd(key, { score, value: JSON.stringify(member) })
    } catch (error) {
      logger.error('Redis zadd error:', error)
      return 0
    }
  },

  async zrange(key, start, stop, withScores = false) {
    try {
      const options = withScores ? { withScores: true } : {}
      const result = await redisClient.zRange(key, start, stop, options)
      
      if (withScores) {
        return result.map(item => ({
          value: JSON.parse(item.value),
          score: item.score
        }))
      } else {
        return result.map(item => JSON.parse(item))
      }
    } catch (error) {
      logger.error('Redis zrange error:', error)
      return []
    }
  },

  // 哈希操作
  async hset(key, field, value) {
    try {
      return await redisClient.hSet(key, field, JSON.stringify(value))
    } catch (error) {
      logger.error('Redis hset error:', error)
      return 0
    }
  },

  async hget(key, field) {
    try {
      const value = await redisClient.hGet(key, field)
      return value ? JSON.parse(value) : null
    } catch (error) {
      logger.error('Redis hget error:', error)
      return null
    }
  },

  async hgetall(key) {
    try {
      const hash = await redisClient.hGetAll(key)
      const result = {}
      for (const [field, value] of Object.entries(hash)) {
        result[field] = JSON.parse(value)
      }
      return result
    } catch (error) {
      logger.error('Redis hgetall error:', error)
      return {}
    }
  }
}

module.exports = {
  redisClient,
  connectRedis,
  closeRedis,
  redisUtils
}
