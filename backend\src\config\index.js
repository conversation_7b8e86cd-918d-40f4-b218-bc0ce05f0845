require('dotenv').config()

const config = {
  // 环境配置
  NODE_ENV: process.env.NODE_ENV || 'development',
  PORT: parseInt(process.env.PORT) || 3001,
  
  // 数据库配置
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 3306,
    username: process.env.DB_USERNAME || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_DATABASE || 'materials_studio_qa',
    dialect: 'mysql',
    timezone: '+08:00',
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    pool: {
      max: 10,
      min: 0,
      acquire: 30000,
      idle: 10000
    }
  },
  
  // Redis配置
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT) || 6379,
    password: process.env.REDIS_PASSWORD || '',
    db: parseInt(process.env.REDIS_DB) || 0,
    keyPrefix: 'ms_qa:',
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3
  },
  
  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET || 'materials-studio-qa-secret-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d'
  },
  
  // 加密配置
  bcrypt: {
    saltRounds: parseInt(process.env.BCRYPT_SALT_ROUNDS) || 12
  },
  
  // 邮件配置
  email: {
    host: process.env.EMAIL_HOST || 'smtp.qq.com',
    port: parseInt(process.env.EMAIL_PORT) || 587,
    secure: process.env.EMAIL_SECURE === 'true',
    auth: {
      user: process.env.EMAIL_USER || '',
      pass: process.env.EMAIL_PASS || ''
    },
    from: process.env.EMAIL_FROM || '<EMAIL>'
  },
  
  // 短信配置
  sms: {
    accessKeyId: process.env.SMS_ACCESS_KEY_ID || '',
    accessKeySecret: process.env.SMS_ACCESS_KEY_SECRET || '',
    signName: process.env.SMS_SIGN_NAME || 'Materials Studio答疑平台',
    templateCode: process.env.SMS_TEMPLATE_CODE || 'SMS_123456789'
  },
  
  // 文件上传配置
  upload: {
    maxFileSize: parseInt(process.env.UPLOAD_MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    uploadDir: process.env.UPLOAD_DIR || 'src/uploads',
    baseUrl: process.env.UPLOAD_BASE_URL || 'http://localhost:3001/uploads'
  },
  
  // 分页配置
  pagination: {
    defaultLimit: 20,
    maxLimit: 100
  },
  
  // 缓存配置
  cache: {
    defaultTTL: 3600, // 1小时
    shortTTL: 300,    // 5分钟
    longTTL: 86400    // 24小时
  },
  
  // 速率限制配置
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100, // 每个IP最多100个请求
    message: '请求过于频繁，请稍后再试'
  },
  
  // 会员配置
  membership: {
    levels: {
      0: { name: '基础用户', features: ['basic_qa', 'basic_search'] },
      1: { name: '初级会员', features: ['full_qa', 'video_access', 'resource_download', 'priority_answer'] },
      2: { name: '高级会员', features: ['all_features', 'expert_consultation', 'unlimited_download'] }
    },
    prices: {
      1: { monthly: 29, yearly: 298 },
      2: { monthly: 99, yearly: 998 }
    }
  },
  
  // 支付配置
  payment: {
    alipay: {
      appId: process.env.ALIPAY_APP_ID || '',
      privateKey: process.env.ALIPAY_PRIVATE_KEY || '',
      publicKey: process.env.ALIPAY_PUBLIC_KEY || '',
      gateway: process.env.ALIPAY_GATEWAY || 'https://openapi.alipay.com/gateway.do'
    },
    wechat: {
      appId: process.env.WECHAT_APP_ID || '',
      mchId: process.env.WECHAT_MCH_ID || '',
      key: process.env.WECHAT_KEY || '',
      certPath: process.env.WECHAT_CERT_PATH || ''
    }
  },
  
  // 日志配置
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || 'logs/app.log',
    maxSize: process.env.LOG_MAX_SIZE || '20m',
    maxFiles: process.env.LOG_MAX_FILES || '14d'
  },
  
  // 安全配置
  security: {
    passwordMinLength: 6,
    passwordMaxLength: 128,
    maxLoginAttempts: 5,
    lockoutTime: 15 * 60 * 1000, // 15分钟
    sessionTimeout: 24 * 60 * 60 * 1000 // 24小时
  },
  
  // 第三方服务配置
  services: {
    qiniu: {
      accessKey: process.env.QINIU_ACCESS_KEY || '',
      secretKey: process.env.QINIU_SECRET_KEY || '',
      bucket: process.env.QINIU_BUCKET || '',
      domain: process.env.QINIU_DOMAIN || ''
    },
    elasticsearch: {
      host: process.env.ES_HOST || 'localhost:9200',
      index: process.env.ES_INDEX || 'ms_qa'
    }
  }
}

// 验证必要的环境变量
const requiredEnvVars = [
  'DB_HOST',
  'DB_USERNAME',
  'DB_PASSWORD',
  'DB_DATABASE',
  'JWT_SECRET'
]

const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar])

if (missingEnvVars.length > 0) {
  console.error('Missing required environment variables:', missingEnvVars.join(', '))
  if (process.env.NODE_ENV === 'production') {
    process.exit(1)
  }
}

module.exports = config
