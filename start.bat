@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: Materials Studio答疑平台启动脚本 (Windows版本)
:: 用于快速启动开发环境

title Materials Studio答疑平台

:: 颜色定义
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

:: 打印带颜色的消息
:print_success
echo %GREEN%%~1%NC%
goto :eof

:print_error
echo %RED%%~1%NC%
goto :eof

:print_warning
echo %YELLOW%%~1%NC%
goto :eof

:print_info
echo %BLUE%%~1%NC%
goto :eof

:: 检查命令是否存在
:command_exists
where %1 >nul 2>&1
goto :eof

:: 检查端口是否被占用
:check_port
netstat -an | find ":%1 " | find "LISTENING" >nul 2>&1
goto :eof

:: 显示帮助信息
:show_help
echo Materials Studio答疑平台启动脚本
echo.
echo 用法: %0 [选项]
echo.
echo 选项:
echo   dev         启动开发环境
echo   prod        启动生产环境 (Docker)
echo   stop        停止所有服务
echo   install     安装依赖
echo   help        显示此帮助信息
echo.
goto :eof

:: 检查依赖
:check_dependencies
call :print_info "检查依赖..."

call :command_exists node
if errorlevel 1 (
    call :print_error "Node.js 未安装，请先安装 Node.js"
    exit /b 1
)

call :command_exists npm
if errorlevel 1 (
    call :print_error "npm 未安装，请先安装 npm"
    exit /b 1
)

call :print_success "依赖检查通过"
goto :eof

:: 安装依赖
:install_dependencies
call :print_info "安装项目依赖..."

:: 安装后端依赖
if exist "backend" (
    call :print_info "安装后端依赖..."
    cd backend
    call npm install
    if errorlevel 1 (
        call :print_error "后端依赖安装失败"
        exit /b 1
    )
    cd ..
    call :print_success "后端依赖安装完成"
)

:: 安装前端依赖
if exist "frontend" (
    call :print_info "安装前端依赖..."
    cd frontend
    call npm install
    if errorlevel 1 (
        call :print_error "前端依赖安装失败"
        exit /b 1
    )
    cd ..
    call :print_success "前端依赖安装完成"
)

call :print_success "所有依赖安装完成"
goto :eof

:: 创建环境配置文件
:create_env_file
if not exist ".env" (
    call :print_info "创建环境配置文件..."
    copy ".env.example" ".env" >nul
    call :print_warning "请编辑 .env 文件配置您的环境变量"
)
goto :eof

:: 启动开发环境
:start_dev
call :print_info "启动开发环境..."

call :check_dependencies
if errorlevel 1 exit /b 1

call :create_env_file

:: 检查端口占用
call :check_port 3000
if not errorlevel 1 (
    call :print_warning "端口 3000 已被占用，前端可能无法启动"
)

call :check_port 3001
if not errorlevel 1 (
    call :print_warning "端口 3001 已被占用，后端可能无法启动"
)

:: 启动后端
call :print_info "启动后端服务..."
cd backend
start "后端服务" cmd /c "npm run dev"
cd ..

:: 等待后端启动
timeout /t 5 /nobreak >nul

:: 启动前端
call :print_info "启动前端服务..."
cd frontend
start "前端服务" cmd /c "npm run serve"
cd ..

:: 等待前端启动
timeout /t 10 /nobreak >nul

call :print_success "开发环境启动完成!"
call :print_info "访问地址:"
call :print_info "  前端应用: http://localhost:3000"
call :print_info "  后端API: http://localhost:3001"
echo.
call :print_info "按任意键停止服务..."
pause >nul

:: 停止服务
call :stop_services
goto :eof

:: 启动生产环境
:start_prod
call :print_info "启动生产环境..."

call :command_exists docker
if errorlevel 1 (
    call :print_error "Docker 未安装，请先安装 Docker Desktop"
    exit /b 1
)

call :command_exists docker-compose
if errorlevel 1 (
    call :print_error "Docker Compose 未安装"
    exit /b 1
)

call :create_env_file

:: 构建并启动所有服务
docker-compose up -d --build

if errorlevel 1 (
    call :print_error "Docker 服务启动失败"
    exit /b 1
)

call :print_success "生产环境启动完成!"
call :print_info "访问地址: http://localhost"
goto :eof

:: 停止服务
:stop_services
call :print_info "停止所有服务..."

:: 停止Docker服务
docker-compose down >nul 2>&1

:: 停止本地进程
taskkill /f /im node.exe >nul 2>&1

call :print_success "所有服务已停止"
goto :eof

:: 主函数
:main
if "%1"=="" goto show_help
if "%1"=="help" goto show_help
if "%1"=="dev" goto start_dev
if "%1"=="prod" goto start_prod
if "%1"=="stop" goto stop_services
if "%1"=="install" goto install_dependencies

call :print_error "未知选项: %1"
goto show_help

:: 运行主函数
call :main %1
