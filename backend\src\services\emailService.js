const nodemailer = require('nodemailer')
const config = require('../config')
const logger = require('../utils/logger')
const { ExternalServiceError } = require('../utils/errors')

class EmailService {
  constructor() {
    this.transporter = null
    this.initializeTransporter()
  }

  // 初始化邮件传输器
  initializeTransporter() {
    try {
      this.transporter = nodemailer.createTransporter({
        host: config.email.host,
        port: config.email.port,
        secure: config.email.secure,
        auth: {
          user: config.email.auth.user,
          pass: config.email.auth.pass
        },
        pool: true,
        maxConnections: 5,
        maxMessages: 100,
        rateLimit: 10 // 每秒最多发送10封邮件
      })

      // 验证连接
      this.transporter.verify((error, success) => {
        if (error) {
          logger.error('Email service initialization failed:', error)
        } else {
          logger.info('Email service initialized successfully')
        }
      })
    } catch (error) {
      logger.error('Failed to create email transporter:', error)
      throw new ExternalServiceError('邮件服务初始化失败', 'email')
    }
  }

  // 发送邮件的通用方法
  async sendEmail(options) {
    try {
      if (!this.transporter) {
        throw new ExternalServiceError('邮件服务未初始化', 'email')
      }

      const mailOptions = {
        from: options.from || config.email.from,
        to: options.to,
        subject: options.subject,
        html: options.html,
        text: options.text,
        attachments: options.attachments
      }

      const result = await this.transporter.sendMail(mailOptions)
      
      logger.info('Email sent successfully', {
        to: options.to,
        subject: options.subject,
        messageId: result.messageId
      })

      return {
        success: true,
        messageId: result.messageId
      }
    } catch (error) {
      logger.error('Failed to send email:', error)
      throw new ExternalServiceError('邮件发送失败', 'email')
    }
  }

  // 发送验证码邮件
  async sendVerificationCode(email, code) {
    const subject = 'Materials Studio答疑平台 - 验证码'
    const html = this.generateVerificationCodeTemplate(code)
    
    return await this.sendEmail({
      to: email,
      subject,
      html
    })
  }

  // 发送邮箱验证邮件
  async sendVerificationEmail(email, token) {
    const verificationUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/verify-email?token=${token}`
    const subject = 'Materials Studio答疑平台 - 邮箱验证'
    const html = this.generateEmailVerificationTemplate(verificationUrl)
    
    return await this.sendEmail({
      to: email,
      subject,
      html
    })
  }

  // 发送密码重置邮件
  async sendPasswordResetEmail(email, token) {
    const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/reset-password?token=${token}`
    const subject = 'Materials Studio答疑平台 - 密码重置'
    const html = this.generatePasswordResetTemplate(resetUrl)
    
    return await this.sendEmail({
      to: email,
      subject,
      html
    })
  }

  // 发送欢迎邮件
  async sendWelcomeEmail(email, username) {
    const subject = '欢迎加入Materials Studio答疑平台'
    const html = this.generateWelcomeTemplate(username)
    
    return await this.sendEmail({
      to: email,
      subject,
      html
    })
  }

  // 发送问题回答通知邮件
  async sendAnswerNotificationEmail(email, questionTitle, answerAuthor) {
    const subject = '您的问题有新回答'
    const html = this.generateAnswerNotificationTemplate(questionTitle, answerAuthor)
    
    return await this.sendEmail({
      to: email,
      subject,
      html
    })
  }

  // 发送会员到期提醒邮件
  async sendMembershipExpiryEmail(email, username, expiryDate) {
    const subject = '会员即将到期提醒'
    const html = this.generateMembershipExpiryTemplate(username, expiryDate)
    
    return await this.sendEmail({
      to: email,
      subject,
      html
    })
  }

  // 生成验证码邮件模板
  generateVerificationCodeTemplate(code) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>验证码</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
          .code { background: #fff; border: 2px dashed #667eea; padding: 20px; text-align: center; font-size: 24px; font-weight: bold; color: #667eea; margin: 20px 0; border-radius: 8px; }
          .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Materials Studio答疑平台</h1>
            <p>您的验证码</p>
          </div>
          <div class="content">
            <p>您好！</p>
            <p>您正在进行身份验证，您的验证码是：</p>
            <div class="code">${code}</div>
            <p>验证码有效期为5分钟，请及时使用。如果这不是您的操作，请忽略此邮件。</p>
            <p>为了保护您的账户安全，请不要将验证码告诉他人。</p>
          </div>
          <div class="footer">
            <p>此邮件由系统自动发送，请勿回复</p>
            <p>© 2024 Materials Studio答疑平台</p>
          </div>
        </div>
      </body>
      </html>
    `
  }

  // 生成邮箱验证模板
  generateEmailVerificationTemplate(verificationUrl) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>邮箱验证</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
          .button { display: inline-block; background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Materials Studio答疑平台</h1>
            <p>邮箱验证</p>
          </div>
          <div class="content">
            <p>您好！</p>
            <p>感谢您注册Materials Studio答疑平台。请点击下面的按钮验证您的邮箱地址：</p>
            <p style="text-align: center;">
              <a href="${verificationUrl}" class="button">验证邮箱</a>
            </p>
            <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
            <p style="word-break: break-all; color: #667eea;">${verificationUrl}</p>
            <p>此链接24小时内有效。如果这不是您的操作，请忽略此邮件。</p>
          </div>
          <div class="footer">
            <p>此邮件由系统自动发送，请勿回复</p>
            <p>© 2024 Materials Studio答疑平台</p>
          </div>
        </div>
      </body>
      </html>
    `
  }

  // 生成密码重置模板
  generatePasswordResetTemplate(resetUrl) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>密码重置</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
          .button { display: inline-block; background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
          .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 6px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Materials Studio答疑平台</h1>
            <p>密码重置</p>
          </div>
          <div class="content">
            <p>您好！</p>
            <p>我们收到了您的密码重置请求。请点击下面的按钮重置您的密码：</p>
            <p style="text-align: center;">
              <a href="${resetUrl}" class="button">重置密码</a>
            </p>
            <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
            <p style="word-break: break-all; color: #667eea;">${resetUrl}</p>
            <div class="warning">
              <strong>安全提醒：</strong>
              <ul>
                <li>此链接1小时内有效</li>
                <li>如果这不是您的操作，请立即联系我们</li>
                <li>为了账户安全，建议设置复杂密码</li>
              </ul>
            </div>
          </div>
          <div class="footer">
            <p>此邮件由系统自动发送，请勿回复</p>
            <p>© 2024 Materials Studio答疑平台</p>
          </div>
        </div>
      </body>
      </html>
    `
  }

  // 生成欢迎邮件模板
  generateWelcomeTemplate(username) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>欢迎加入</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
          .feature { background: white; padding: 20px; margin: 15px 0; border-radius: 6px; border-left: 4px solid #667eea; }
          .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 欢迎加入Materials Studio答疑平台！</h1>
          </div>
          <div class="content">
            <p>亲爱的 ${username}，</p>
            <p>欢迎加入Materials Studio答疑平台！我们很高兴您成为我们社区的一员。</p>
            
            <div class="feature">
              <h3>🔬 专业问答</h3>
              <p>在这里您可以提出Materials Studio相关问题，获得专业解答</p>
            </div>
            
            <div class="feature">
              <h3>📚 丰富资源</h3>
              <p>访问大量的教程、模板和案例分析资源</p>
            </div>
            
            <div class="feature">
              <h3>👥 专家社区</h3>
              <p>与领域专家和同行交流，分享经验和见解</p>
            </div>
            
            <p>现在就开始您的学习之旅吧！如果您有任何问题，随时联系我们的客服团队。</p>
          </div>
          <div class="footer">
            <p>此邮件由系统自动发送，请勿回复</p>
            <p>© 2024 Materials Studio答疑平台</p>
          </div>
        </div>
      </body>
      </html>
    `
  }

  // 生成回答通知模板
  generateAnswerNotificationTemplate(questionTitle, answerAuthor) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>新回答通知</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
          .question { background: white; padding: 20px; border-radius: 6px; border-left: 4px solid #667eea; margin: 20px 0; }
          .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>💬 您的问题有新回答</h1>
          </div>
          <div class="content">
            <p>您好！</p>
            <p>您的问题收到了新的回答：</p>
            
            <div class="question">
              <h3>${questionTitle}</h3>
              <p><strong>回答者：</strong>${answerAuthor}</p>
            </div>
            
            <p>快去查看这个回答吧！也许正是您需要的解决方案。</p>
          </div>
          <div class="footer">
            <p>此邮件由系统自动发送，请勿回复</p>
            <p>© 2024 Materials Studio答疑平台</p>
          </div>
        </div>
      </body>
      </html>
    `
  }

  // 生成会员到期提醒模板
  generateMembershipExpiryTemplate(username, expiryDate) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>会员到期提醒</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
          .expiry-info { background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 6px; margin: 20px 0; text-align: center; }
          .button { display: inline-block; background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>⏰ 会员到期提醒</h1>
          </div>
          <div class="content">
            <p>亲爱的 ${username}，</p>
            <p>您的会员权益即将到期，请及时续费以继续享受会员服务。</p>
            
            <div class="expiry-info">
              <h3>到期时间：${expiryDate}</h3>
              <p>续费后可继续享受：</p>
              <ul style="text-align: left; display: inline-block;">
                <li>查看完整问答内容</li>
                <li>下载专业资源</li>
                <li>专家一对一咨询</li>
                <li>优先获得回答</li>
              </ul>
            </div>
            
            <p style="text-align: center;">
              <a href="${process.env.FRONTEND_URL || 'http://localhost:3000'}/membership" class="button">立即续费</a>
            </p>
          </div>
          <div class="footer">
            <p>此邮件由系统自动发送，请勿回复</p>
            <p>© 2024 Materials Studio答疑平台</p>
          </div>
        </div>
      </body>
      </html>
    `
  }
}

module.exports = new EmailService()
