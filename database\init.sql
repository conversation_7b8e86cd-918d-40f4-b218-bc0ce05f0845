-- Materials Studio答疑平台数据库初始化脚本
-- 创建数据库和基础表结构

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `materials_studio_qa` 
DEFAULT CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE `materials_studio_qa`;

-- 用户表
CREATE TABLE IF NOT EXISTS `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `avatar` varchar(500) DEFAULT NULL,
  `nickname` varchar(50) DEFAULT NULL,
  `bio` text,
  `gender` enum('male','female','other') DEFAULT NULL,
  `birthday` date DEFAULT NULL,
  `location` varchar(100) DEFAULT NULL,
  `website` varchar(255) DEFAULT NULL,
  `role` enum('user','expert','admin') NOT NULL DEFAULT 'user',
  `status` enum('active','inactive','banned') NOT NULL DEFAULT 'active',
  `member_level` int NOT NULL DEFAULT '0' COMMENT '0: 基础用户, 1: 初级会员, 2: 高级会员',
  `member_expired_at` datetime DEFAULT NULL COMMENT '会员到期时间',
  `email_verified` tinyint(1) NOT NULL DEFAULT '0',
  `phone_verified` tinyint(1) NOT NULL DEFAULT '0',
  `email_verification_token` varchar(255) DEFAULT NULL,
  `password_reset_token` varchar(255) DEFAULT NULL,
  `password_reset_expires` datetime DEFAULT NULL,
  `last_login_at` datetime DEFAULT NULL,
  `last_login_ip` varchar(45) DEFAULT NULL,
  `login_attempts` int NOT NULL DEFAULT '0',
  `lock_until` datetime DEFAULT NULL,
  `points` int NOT NULL DEFAULT '0' COMMENT '用户积分',
  `reputation` int NOT NULL DEFAULT '0' COMMENT '用户声誉值',
  `question_count` int NOT NULL DEFAULT '0' COMMENT '提问数量',
  `answer_count` int NOT NULL DEFAULT '0' COMMENT '回答数量',
  `accepted_answer_count` int NOT NULL DEFAULT '0' COMMENT '被采纳回答数量',
  `settings` json DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `phone` (`phone`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`),
  KEY `idx_member_level` (`member_level`),
  KEY `idx_reputation` (`reputation`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 分类表
CREATE TABLE IF NOT EXISTS `categories` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL,
  `description` text,
  `icon` varchar(255) DEFAULT NULL,
  `color` varchar(7) DEFAULT NULL,
  `parent_id` int DEFAULT NULL,
  `sort_order` int NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `question_count` int NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 问题表
CREATE TABLE IF NOT EXISTS `questions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL,
  `content` text NOT NULL,
  `brief` varchar(500) DEFAULT NULL COMMENT '问题简介，用于列表展示',
  `user_id` int NOT NULL,
  `category_id` int DEFAULT NULL,
  `tags` json DEFAULT NULL COMMENT '问题标签数组',
  `status` enum('draft','pending','published','closed','deleted') NOT NULL DEFAULT 'published',
  `priority` enum('low','normal','high','urgent') NOT NULL DEFAULT 'normal',
  `difficulty` enum('beginner','intermediate','advanced','expert') DEFAULT NULL COMMENT '问题难度级别',
  `is_top` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否置顶',
  `is_essence` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否精华',
  `is_anonymous` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否匿名提问',
  `require_membership` int NOT NULL DEFAULT '0' COMMENT '查看所需会员等级，0表示免费',
  `view_count` int NOT NULL DEFAULT '0' COMMENT '浏览次数',
  `answer_count` int NOT NULL DEFAULT '0' COMMENT '回答数量',
  `favorite_count` int NOT NULL DEFAULT '0' COMMENT '收藏次数',
  `like_count` int NOT NULL DEFAULT '0' COMMENT '点赞次数',
  `dislike_count` int NOT NULL DEFAULT '0' COMMENT '点踩次数',
  `score` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '问题评分',
  `accepted_answer_id` int DEFAULT NULL COMMENT '被采纳的回答ID',
  `last_answer_at` datetime DEFAULT NULL COMMENT '最后回答时间',
  `last_answer_user_id` int DEFAULT NULL COMMENT '最后回答用户ID',
  `closed_at` datetime DEFAULT NULL COMMENT '关闭时间',
  `closed_reason` varchar(500) DEFAULT NULL COMMENT '关闭原因',
  `attachments` json DEFAULT NULL COMMENT '附件列表',
  `metadata` json DEFAULT NULL COMMENT '扩展元数据',
  `search_vector` text COMMENT '搜索向量，用于全文搜索',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`),
  KEY `idx_top_essence` (`is_top`,`is_essence`),
  KEY `idx_require_membership` (`require_membership`),
  KEY `idx_view_count` (`view_count`),
  KEY `idx_answer_count` (`answer_count`),
  KEY `idx_score` (`score`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_last_answer_at` (`last_answer_at`),
  KEY `idx_accepted_answer_id` (`accepted_answer_id`),
  FULLTEXT KEY `idx_search` (`title`,`content`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 回答表
CREATE TABLE IF NOT EXISTS `answers` (
  `id` int NOT NULL AUTO_INCREMENT,
  `question_id` int NOT NULL,
  `user_id` int NOT NULL,
  `content` text NOT NULL,
  `is_accepted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否被采纳',
  `is_anonymous` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否匿名回答',
  `like_count` int NOT NULL DEFAULT '0' COMMENT '点赞次数',
  `dislike_count` int NOT NULL DEFAULT '0' COMMENT '点踩次数',
  `score` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '回答评分',
  `attachments` json DEFAULT NULL COMMENT '附件列表',
  `metadata` json DEFAULT NULL COMMENT '扩展元数据',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_question_id` (`question_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_is_accepted` (`is_accepted`),
  KEY `idx_score` (`score`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 评论表
CREATE TABLE IF NOT EXISTS `comments` (
  `id` int NOT NULL AUTO_INCREMENT,
  `target_type` enum('question','answer') NOT NULL COMMENT '评论目标类型',
  `target_id` int NOT NULL COMMENT '评论目标ID',
  `user_id` int NOT NULL,
  `parent_id` int DEFAULT NULL COMMENT '父评论ID，用于回复',
  `content` text NOT NULL,
  `like_count` int NOT NULL DEFAULT '0',
  `is_anonymous` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_target` (`target_type`,`target_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 资源表
CREATE TABLE IF NOT EXISTS `resources` (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL,
  `description` text,
  `type` enum('document','video','template','software') NOT NULL,
  `category_id` int DEFAULT NULL,
  `file_url` varchar(500) NOT NULL,
  `file_size` bigint DEFAULT NULL,
  `file_type` varchar(100) DEFAULT NULL,
  `thumbnail` varchar(500) DEFAULT NULL,
  `user_id` int NOT NULL COMMENT '上传用户ID',
  `require_membership` int NOT NULL DEFAULT '0' COMMENT '下载所需会员等级',
  `download_count` int NOT NULL DEFAULT '0',
  `like_count` int NOT NULL DEFAULT '0',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '资源价格，0表示免费',
  `tags` json DEFAULT NULL,
  `status` enum('pending','published','rejected','deleted') NOT NULL DEFAULT 'pending',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_require_membership` (`require_membership`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 会员订单表
CREATE TABLE IF NOT EXISTS `membership_orders` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `order_no` varchar(32) NOT NULL COMMENT '订单号',
  `membership_level` int NOT NULL COMMENT '会员等级',
  `duration_type` enum('monthly','yearly') NOT NULL COMMENT '时长类型',
  `duration_months` int NOT NULL COMMENT '时长（月）',
  `original_price` decimal(10,2) NOT NULL COMMENT '原价',
  `discount_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '优惠金额',
  `final_price` decimal(10,2) NOT NULL COMMENT '实付金额',
  `payment_method` enum('alipay','wechat','bank') DEFAULT NULL,
  `payment_status` enum('pending','paid','failed','refunded') NOT NULL DEFAULT 'pending',
  `payment_time` datetime DEFAULT NULL,
  `payment_transaction_id` varchar(100) DEFAULT NULL,
  `status` enum('pending','completed','cancelled','refunded') NOT NULL DEFAULT 'pending',
  `expires_at` datetime DEFAULT NULL COMMENT '会员到期时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 用户行为记录表
CREATE TABLE IF NOT EXISTS `user_actions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `action_type` varchar(50) NOT NULL COMMENT '行为类型：view, like, favorite, download等',
  `target_type` varchar(50) NOT NULL COMMENT '目标类型：question, answer, resource等',
  `target_id` int NOT NULL COMMENT '目标ID',
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `metadata` json DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action_type` (`action_type`),
  KEY `idx_target` (`target_type`,`target_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 通知表
CREATE TABLE IF NOT EXISTS `notifications` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `type` varchar(50) NOT NULL COMMENT '通知类型',
  `title` varchar(200) NOT NULL,
  `content` text,
  `data` json DEFAULT NULL COMMENT '通知相关数据',
  `is_read` tinyint(1) NOT NULL DEFAULT '0',
  `read_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 系统设置表
CREATE TABLE IF NOT EXISTS `system_settings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `key` varchar(100) NOT NULL,
  `value` text,
  `type` enum('string','number','boolean','json') NOT NULL DEFAULT 'string',
  `description` varchar(500) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `key` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入默认分类数据
INSERT INTO `categories` (`name`, `slug`, `description`, `icon`, `color`, `sort_order`) VALUES
('Materials Studio基础', 'ms-basics', 'Materials Studio软件基础操作和入门教程', 'book', '#3498db', 1),
('分子动力学', 'molecular-dynamics', '分子动力学模拟相关问题', 'atom', '#e74c3c', 2),
('DFT计算', 'dft-calculation', '密度泛函理论计算相关问题', 'calculator', '#f39c12', 3),
('晶体结构', 'crystal-structure', '晶体结构建模和分析', 'cube', '#9b59b6', 4),
('表面与界面', 'surface-interface', '表面和界面性质研究', 'layer-group', '#1abc9c', 5),
('高分子材料', 'polymer-materials', '高分子材料模拟和分析', 'dna', '#34495e', 6),
('纳米材料', 'nanomaterials', '纳米材料设计和性能预测', 'microscope', '#e67e22', 7),
('软件技巧', 'software-tips', 'Materials Studio使用技巧和经验分享', 'lightbulb', '#2ecc71', 8);

-- 插入默认系统设置
INSERT INTO `system_settings` (`key`, `value`, `type`, `description`) VALUES
('site_name', 'Materials Studio答疑平台', 'string', '网站名称'),
('site_description', '专业的Materials Studio软件学习与问答社区', 'string', '网站描述'),
('site_keywords', 'Materials Studio,分子模拟,问答平台,学术交流', 'string', '网站关键词'),
('registration_enabled', 'true', 'boolean', '是否允许用户注册'),
('email_verification_required', 'true', 'boolean', '是否需要邮箱验证'),
('question_approval_required', 'false', 'boolean', '问题是否需要审核'),
('answer_approval_required', 'false', 'boolean', '回答是否需要审核'),
('max_question_tags', '5', 'number', '问题最大标签数'),
('max_file_size', '10485760', 'number', '最大文件上传大小（字节）'),
('points_for_question', '10', 'number', '提问获得积分'),
('points_for_answer', '20', 'number', '回答获得积分'),
('points_for_accepted_answer', '50', 'number', '回答被采纳获得积分'),
('points_for_daily_login', '5', 'number', '每日登录获得积分');

-- 创建管理员账户（密码：Admin@123）
INSERT INTO `users` (`username`, `email`, `password`, `role`, `status`, `email_verified`, `reputation`, `points`) VALUES
('admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBdXig/8VfuFvO', 'admin', 'active', 1, 1000, 1000);

SET FOREIGN_KEY_CHECKS = 1;
