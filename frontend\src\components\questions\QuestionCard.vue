<template>
  <div class="question-card" @click="handleClick">
    <!-- 问题状态标签 -->
    <div class="question-status" v-if="question.status">
      <el-tag 
        :type="getStatusType(question.status)" 
        size="small"
        effect="light"
      >
        {{ getStatusText(question.status) }}
      </el-tag>
      <el-tag 
        v-if="question.isEssence" 
        type="warning" 
        size="small"
        effect="light"
      >
        精华
      </el-tag>
      <el-tag 
        v-if="question.isTop" 
        type="danger" 
        size="small"
        effect="light"
      >
        置顶
      </el-tag>
    </div>

    <!-- 问题标题 -->
    <h3 class="question-title">
      {{ question.title }}
    </h3>

    <!-- 问题简介 -->
    <p class="question-brief" v-if="question.brief">
      {{ question.brief }}
    </p>

    <!-- 问题标签 -->
    <div class="question-tags" v-if="question.tags && question.tags.length">
      <el-tag 
        v-for="tag in question.tags.slice(0, 3)" 
        :key="tag"
        size="small"
        effect="plain"
        class="tag-item"
      >
        {{ tag }}
      </el-tag>
      <span v-if="question.tags.length > 3" class="more-tags">
        +{{ question.tags.length - 3 }}
      </span>
    </div>

    <!-- 问题元信息 -->
    <div class="question-meta">
      <!-- 用户信息 -->
      <div class="user-info">
        <el-avatar 
          :src="question.userAvatar" 
          :size="24"
          class="user-avatar"
        >
          {{ question.username?.charAt(0) }}
        </el-avatar>
        <span class="username">{{ question.username }}</span>
        <span class="user-level" v-if="question.userLevel">
          <el-icon><Crown /></el-icon>
        </span>
      </div>

      <!-- 统计信息 -->
      <div class="question-stats">
        <div class="stat-item">
          <el-icon><View /></el-icon>
          <span>{{ formatNumber(question.viewCount) }}</span>
        </div>
        <div class="stat-item">
          <el-icon><ChatDotRound /></el-icon>
          <span>{{ question.answerCount || 0 }}</span>
        </div>
        <div class="stat-item" v-if="question.favoriteCount">
          <el-icon><Star /></el-icon>
          <span>{{ formatNumber(question.favoriteCount) }}</span>
        </div>
      </div>
    </div>

    <!-- 时间信息 -->
    <div class="question-time">
      <span class="created-time">
        {{ formatTime(question.createdAt) }}
      </span>
      <span class="updated-time" v-if="question.updatedAt !== question.createdAt">
        更新于 {{ formatTime(question.updatedAt) }}
      </span>
    </div>

    <!-- 回答状态指示器 -->
    <div class="answer-indicator" v-if="question.hasAcceptedAnswer">
      <el-icon class="accepted-icon"><Check /></el-icon>
      <span>已解决</span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'
import {
  Crown,
  View,
  ChatDotRound,
  Star,
  Check
} from '@element-plus/icons-vue'

dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

const props = defineProps({
  question: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['click'])

// 处理点击事件
const handleClick = () => {
  emit('click', props.question)
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    0: 'info',     // 待审核
    1: 'success',  // 已发布
    2: 'warning',  // 已关闭
    3: 'success'   // 已解决
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    0: '待审核',
    1: '已发布',
    2: '已关闭',
    3: '已解决'
  }
  return statusMap[status] || '未知'
}

// 格式化数字
const formatNumber = (num) => {
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

// 格式化时间
const formatTime = (time) => {
  if (!time) return ''
  const now = dayjs()
  const target = dayjs(time)
  const diffDays = now.diff(target, 'day')
  
  if (diffDays === 0) {
    return target.fromNow()
  } else if (diffDays < 7) {
    return target.fromNow()
  } else {
    return target.format('YYYY-MM-DD')
  }
}
</script>

<style lang="scss" scoped>
.question-card {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: 24px;
  cursor: pointer;
  transition: all var(--transition-normal);
  border: 1px solid var(--border-light);
  position: relative;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
  }
}

.question-status {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.question-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  
  &:hover {
    color: var(--primary-color);
  }
}

.question-brief {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.question-tags {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
  align-items: center;
  
  .tag-item {
    background: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
    border: 1px solid rgba(102, 126, 234, 0.2);
  }
  
  .more-tags {
    color: var(--text-light);
    font-size: 0.875rem;
  }
}

.question-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  
  @media (max-width: 480px) {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .user-avatar {
    border: 2px solid var(--border-light);
  }
  
  .username {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.875rem;
  }
  
  .user-level {
    color: var(--warning-color);
    
    .el-icon {
      font-size: 14px;
    }
  }
}

.question-stats {
  display: flex;
  gap: 16px;
  
  .stat-item {
    display: flex;
    align-items: center;
    gap: 4px;
    color: var(--text-light);
    font-size: 0.875rem;
    
    .el-icon {
      font-size: 14px;
    }
  }
}

.question-time {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
  color: var(--text-light);
  
  @media (max-width: 480px) {
    flex-direction: column;
    gap: 4px;
    align-items: flex-start;
  }
}

.answer-indicator {
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
  align-items: center;
  gap: 4px;
  background: var(--success-color);
  color: white;
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
  
  .accepted-icon {
    font-size: 12px;
  }
}

// 响应式调整
@media (max-width: 768px) {
  .question-card {
    padding: 20px;
  }
  
  .question-title {
    font-size: 1rem;
  }
  
  .question-brief {
    font-size: 0.875rem;
  }
}
</style>
