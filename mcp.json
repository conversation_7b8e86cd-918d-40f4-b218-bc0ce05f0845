{"mcpServers": {"sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "files": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "D:/aib<PERSON><PERSON><PERSON>"]}, "filesystem_stdio": {"command": "node", "args": ["/path/to/filesystem-mcp-server/dist/index.js"], "env": {"MCP_LOG_LEVEL": "debug"}, "disabled": false, "autoApprove": []}, "@wopal-mcp-server-hotnews": {"command": "npx", "args": ["-y", "@wopal/mcp-server-hotnews"]}, "playwright": {"command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"]}, "context7": {"url": "https://mcp.context7.com/mcp"}, "smithery": {"url": "https://server.smithery.ai/@cyanheads/filesystem-mcp-server/mcp?api_key=eca8c002-2ce2-4873-88ab-77e96da418e7"}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"]}, "desktop-commander": {"command": "npx", "args": ["-y", "@wonderwhy-er/desktop-commander"]}, "fetch": {"url": "https://server.smithery.ai/@smithery-ai/fetch/mcp?api_key=eca8c002-2ce2-4873-88ab-77e96da418e7"}}}