import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/api'
import { ElMessage } from 'element-plus'

export const useQuestionsStore = defineStore('questions', () => {
  // 状态
  const questions = ref([])
  const currentQuestion = ref(null)
  const categories = ref([])
  const tags = ref([])
  const isLoading = ref(false)
  const pagination = ref({
    page: 1,
    limit: 10,
    total: 0
  })
  const filters = ref({
    category: null,
    tag: null,
    keyword: '',
    sort: 'created_at',
    order: 'desc'
  })

  // 计算属性
  const hasMore = computed(() => {
    return pagination.value.page * pagination.value.limit < pagination.value.total
  })

  // 方法
  const fetchQuestions = async (params = {}) => {
    try {
      isLoading.value = true
      const queryParams = {
        page: pagination.value.page,
        limit: pagination.value.limit,
        ...filters.value,
        ...params
      }
      
      const response = await api.get('/questions', { params: queryParams })
      const { questions: questionList, total, page, limit } = response.data.data
      
      if (page === 1) {
        questions.value = questionList
      } else {
        questions.value.push(...questionList)
      }
      
      pagination.value = { page, limit, total }
      
      return questionList
    } catch (error) {
      console.error('Fetch questions error:', error)
      ElMessage.error('获取问题列表失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const fetchQuestionDetail = async (id) => {
    try {
      isLoading.value = true
      const response = await api.get(`/questions/${id}`)
      currentQuestion.value = response.data.data
      return response.data.data
    } catch (error) {
      console.error('Fetch question detail error:', error)
      ElMessage.error('获取问题详情失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const createQuestion = async (questionData) => {
    try {
      isLoading.value = true
      const response = await api.post('/questions', questionData)
      ElMessage.success('问题发布成功')
      return response.data.data
    } catch (error) {
      const message = error.response?.data?.message || '发布失败'
      ElMessage.error(message)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const updateQuestion = async (id, questionData) => {
    try {
      isLoading.value = true
      const response = await api.put(`/questions/${id}`, questionData)
      
      // 更新本地数据
      const index = questions.value.findIndex(q => q.id === id)
      if (index !== -1) {
        questions.value[index] = response.data.data
      }
      
      if (currentQuestion.value?.id === id) {
        currentQuestion.value = response.data.data
      }
      
      ElMessage.success('问题更新成功')
      return response.data.data
    } catch (error) {
      const message = error.response?.data?.message || '更新失败'
      ElMessage.error(message)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const deleteQuestion = async (id) => {
    try {
      await api.delete(`/questions/${id}`)
      
      // 从本地数据中移除
      questions.value = questions.value.filter(q => q.id !== id)
      
      if (currentQuestion.value?.id === id) {
        currentQuestion.value = null
      }
      
      ElMessage.success('问题删除成功')
    } catch (error) {
      const message = error.response?.data?.message || '删除失败'
      ElMessage.error(message)
      throw error
    }
  }

  const fetchCategories = async () => {
    try {
      const response = await api.get('/categories')
      categories.value = response.data.data
      return response.data.data
    } catch (error) {
      console.error('Fetch categories error:', error)
      throw error
    }
  }

  const fetchTags = async () => {
    try {
      const response = await api.get('/tags')
      tags.value = response.data.data
      return response.data.data
    } catch (error) {
      console.error('Fetch tags error:', error)
      throw error
    }
  }

  const searchQuestions = async (keyword) => {
    filters.value.keyword = keyword
    pagination.value.page = 1
    return await fetchQuestions()
  }

  const filterByCategory = async (categoryId) => {
    filters.value.category = categoryId
    pagination.value.page = 1
    return await fetchQuestions()
  }

  const filterByTag = async (tagName) => {
    filters.value.tag = tagName
    pagination.value.page = 1
    return await fetchQuestions()
  }

  const sortQuestions = async (sort, order = 'desc') => {
    filters.value.sort = sort
    filters.value.order = order
    pagination.value.page = 1
    return await fetchQuestions()
  }

  const loadMore = async () => {
    if (hasMore.value && !isLoading.value) {
      pagination.value.page += 1
      return await fetchQuestions()
    }
  }

  const resetFilters = () => {
    filters.value = {
      category: null,
      tag: null,
      keyword: '',
      sort: 'created_at',
      order: 'desc'
    }
    pagination.value.page = 1
  }

  const favoriteQuestion = async (id) => {
    try {
      await api.post(`/questions/${id}/favorite`)
      
      // 更新本地数据
      const question = questions.value.find(q => q.id === id)
      if (question) {
        question.isFavorite = true
        question.favoriteCount += 1
      }
      
      if (currentQuestion.value?.id === id) {
        currentQuestion.value.isFavorite = true
        currentQuestion.value.favoriteCount += 1
      }
      
      ElMessage.success('收藏成功')
    } catch (error) {
      const message = error.response?.data?.message || '收藏失败'
      ElMessage.error(message)
      throw error
    }
  }

  const unfavoriteQuestion = async (id) => {
    try {
      await api.delete(`/questions/${id}/favorite`)
      
      // 更新本地数据
      const question = questions.value.find(q => q.id === id)
      if (question) {
        question.isFavorite = false
        question.favoriteCount -= 1
      }
      
      if (currentQuestion.value?.id === id) {
        currentQuestion.value.isFavorite = false
        currentQuestion.value.favoriteCount -= 1
      }
      
      ElMessage.success('取消收藏成功')
    } catch (error) {
      const message = error.response?.data?.message || '取消收藏失败'
      ElMessage.error(message)
      throw error
    }
  }

  return {
    // 状态
    questions: readonly(questions),
    currentQuestion: readonly(currentQuestion),
    categories: readonly(categories),
    tags: readonly(tags),
    isLoading: readonly(isLoading),
    pagination: readonly(pagination),
    filters: readonly(filters),
    
    // 计算属性
    hasMore,
    
    // 方法
    fetchQuestions,
    fetchQuestionDetail,
    createQuestion,
    updateQuestion,
    deleteQuestion,
    fetchCategories,
    fetchTags,
    searchQuestions,
    filterByCategory,
    filterByTag,
    sortQuestions,
    loadMore,
    resetFilters,
    favoriteQuestion,
    unfavoriteQuestion
  }
})
