version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: ms-qa-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-root123456}
      MYSQL_DATABASE: ${DB_DATABASE:-materials_studio_qa}
      MYSQL_USER: ${DB_USERNAME:-msqa}
      MYSQL_PASSWORD: ${DB_PASSWORD:-msqa123456}
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    ports:
      - "${DB_PORT:-3306}:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
      - ./database/my.cnf:/etc/mysql/conf.d/my.cnf:ro
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - ms-qa-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: ms-qa-redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
      - ./database/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - ms-qa-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 10s
      retries: 5

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ms-qa-backend
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-production}
      PORT: 3001
      DB_HOST: mysql
      DB_PORT: 3306
      DB_USERNAME: ${DB_USERNAME:-msqa}
      DB_PASSWORD: ${DB_PASSWORD:-msqa123456}
      DB_DATABASE: ${DB_DATABASE:-materials_studio_qa}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: ${JWT_SECRET:-materials-studio-qa-secret-key-change-in-production}
      EMAIL_HOST: ${EMAIL_HOST}
      EMAIL_PORT: ${EMAIL_PORT}
      EMAIL_USER: ${EMAIL_USER}
      EMAIL_PASS: ${EMAIL_PASS}
      UPLOAD_BASE_URL: ${UPLOAD_BASE_URL:-http://localhost:3001/uploads}
    ports:
      - "${BACKEND_PORT:-3001}:3001"
    volumes:
      - ./backend/src/uploads:/app/src/uploads
      - ./backend/src/logs:/app/src/logs
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - ms-qa-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      timeout: 10s
      retries: 5
      start_period: 30s

  # 前端应用
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        VITE_API_BASE_URL: ${VITE_API_BASE_URL:-http://localhost:3001/api}
    container_name: ms-qa-frontend
    restart: unless-stopped
    ports:
      - "${FRONTEND_PORT:-3000}:80"
    depends_on:
      - backend
    networks:
      - ms-qa-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      timeout: 10s
      retries: 5

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: ms-qa-nginx
    restart: unless-stopped
    ports:
      - "${HTTP_PORT:-80}:80"
      - "${HTTPS_PORT:-443}:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./backend/src/uploads:/var/www/uploads:ro
    depends_on:
      - frontend
      - backend
    networks:
      - ms-qa-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      timeout: 10s
      retries: 5

  # Elasticsearch (可选，用于高级搜索)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: ms-qa-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "${ES_PORT:-9200}:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - ms-qa-network
    profiles:
      - search
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9200/_cluster/health"]
      timeout: 30s
      retries: 10

  # Kibana (可选，用于日志分析)
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: ms-qa-kibana
    restart: unless-stopped
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    ports:
      - "${KIBANA_PORT:-5601}:5601"
    depends_on:
      - elasticsearch
    networks:
      - ms-qa-network
    profiles:
      - search
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5601/api/status"]
      timeout: 30s
      retries: 10

  # Prometheus监控 (可选)
  prometheus:
    image: prom/prometheus:latest
    container_name: ms-qa-prometheus
    restart: unless-stopped
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - ms-qa-network
    profiles:
      - monitoring

  # Grafana仪表板 (可选)
  grafana:
    image: grafana/grafana:latest
    container_name: ms-qa-grafana
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin123456}
    ports:
      - "${GRAFANA_PORT:-3003}:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - ms-qa-network
    profiles:
      - monitoring

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  elasticsearch_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  ms-qa-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
