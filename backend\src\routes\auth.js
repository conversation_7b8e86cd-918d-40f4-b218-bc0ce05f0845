const express = require('express')
const { body } = require('express-validator')
const authController = require('../controllers/authController')
const { validateRequest } = require('../middleware/validation')
const { rateLimitAuth } = require('../middleware/rateLimiter')

const router = express.Router()

// 用户注册
router.post('/register', 
  rateLimitAuth,
  [
    body('username')
      .isLength({ min: 3, max: 50 })
      .withMessage('用户名长度必须在3-50个字符之间')
      .matches(/^[a-zA-Z0-9_]+$/)
      .withMessage('用户名只能包含字母、数字和下划线'),
    body('email')
      .isEmail()
      .withMessage('请输入有效的邮箱地址')
      .normalizeEmail(),
    body('password')
      .isLength({ min: 6, max: 128 })
      .withMessage('密码长度必须在6-128个字符之间')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
      .withMessage('密码必须包含至少一个大写字母、一个小写字母和一个数字'),
    body('phone')
      .optional()
      .matches(/^1[3-9]\d{9}$/)
      .withMessage('请输入有效的手机号码'),
    body('code')
      .optional()
      .isLength({ min: 6, max: 6 })
      .withMessage('验证码必须是6位数字')
  ],
  validateRequest,
  authController.register
)

// 用户登录
router.post('/login',
  rateLimitAuth,
  [
    body('loginType')
      .isIn(['password', 'code'])
      .withMessage('登录类型无效'),
    body('username')
      .notEmpty()
      .withMessage('请输入用户名、邮箱或手机号'),
    body('password')
      .if(body('loginType').equals('password'))
      .notEmpty()
      .withMessage('请输入密码'),
    body('code')
      .if(body('loginType').equals('code'))
      .isLength({ min: 6, max: 6 })
      .withMessage('验证码必须是6位数字')
  ],
  validateRequest,
  authController.login
)

// 发送验证码
router.post('/send-code',
  rateLimitAuth,
  [
    body('target')
      .notEmpty()
      .withMessage('请输入手机号或邮箱'),
    body('type')
      .isIn([1, 2, 3]) // 1: 注册, 2: 登录, 3: 重置密码
      .withMessage('验证码类型无效')
  ],
  validateRequest,
  authController.sendVerificationCode
)

// 验证邮箱
router.get('/verify-email/:token',
  authController.verifyEmail
)

// 忘记密码
router.post('/forgot-password',
  rateLimitAuth,
  [
    body('email')
      .isEmail()
      .withMessage('请输入有效的邮箱地址')
      .normalizeEmail()
  ],
  validateRequest,
  authController.forgotPassword
)

// 重置密码
router.post('/reset-password',
  rateLimitAuth,
  [
    body('token')
      .notEmpty()
      .withMessage('重置令牌不能为空'),
    body('password')
      .isLength({ min: 6, max: 128 })
      .withMessage('密码长度必须在6-128个字符之间')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
      .withMessage('密码必须包含至少一个大写字母、一个小写字母和一个数字')
  ],
  validateRequest,
  authController.resetPassword
)

// 刷新令牌
router.post('/refresh-token',
  authController.refreshToken
)

// 退出登录
router.post('/logout',
  authController.logout
)

module.exports = router
