const jwt = require('jsonwebtoken')
const crypto = require('crypto')
const User = require('../models/User')
const config = require('../config')
const logger = require('../utils/logger')
const { redisUtils } = require('../config/redis')
const emailService = require('../services/emailService')
const smsService = require('../services/smsService')
const { ApiError } = require('../utils/errors')

class AuthController {
  // 用户注册
  async register(req, res, next) {
    try {
      const { username, email, password, phone, code } = req.body

      // 检查用户名是否已存在
      const existingUser = await User.findOne({
        where: {
          [User.sequelize.Sequelize.Op.or]: [
            { username },
            { email },
            ...(phone ? [{ phone }] : [])
          ]
        }
      })

      if (existingUser) {
        if (existingUser.username === username) {
          throw new ApiError(400, '用户名已存在')
        }
        if (existingUser.email === email) {
          throw new ApiError(400, '邮箱已被注册')
        }
        if (existingUser.phone === phone) {
          throw new ApiError(400, '手机号已被注册')
        }
      }

      // 如果提供了手机号和验证码，验证验证码
      if (phone && code) {
        const isValidCode = await this.verifyCode(phone, code, 1)
        if (!isValidCode) {
          throw new ApiError(400, '验证码无效或已过期')
        }
      }

      // 创建用户
      const user = await User.create({
        username,
        email,
        password,
        phone,
        phoneVerified: !!(phone && code),
        emailVerificationToken: crypto.randomBytes(32).toString('hex')
      })

      // 发送邮箱验证邮件
      if (!user.emailVerified) {
        await emailService.sendVerificationEmail(user.email, user.emailVerificationToken)
      }

      // 清除验证码缓存
      if (phone && code) {
        await redisUtils.del(`verification_code:${phone}:1`)
      }

      logger.info(`User registered: ${user.id} - ${user.username}`)

      res.status(201).json({
        code: 200,
        message: '注册成功',
        data: {
          user: {
            id: user.id,
            username: user.username,
            email: user.email,
            emailVerified: user.emailVerified
          }
        }
      })
    } catch (error) {
      next(error)
    }
  }

  // 用户登录
  async login(req, res, next) {
    try {
      const { loginType, username, password, code } = req.body
      const clientIp = req.ip || req.connection.remoteAddress

      // 查找用户
      const user = await User.findByEmailOrUsername(username)
      if (!user) {
        throw new ApiError(400, '用户不存在')
      }

      // 检查账户状态
      if (user.status !== 'active') {
        throw new ApiError(400, '账户已被禁用')
      }

      // 检查账户是否被锁定
      if (user.isLocked()) {
        throw new ApiError(400, '账户已被锁定，请稍后再试')
      }

      let isValidCredential = false

      if (loginType === 'password') {
        // 密码登录
        isValidCredential = await user.validatePassword(password)
      } else if (loginType === 'code') {
        // 验证码登录
        isValidCredential = await this.verifyCode(username, code, 2)
      }

      if (!isValidCredential) {
        // 增加登录尝试次数
        await user.incrementLoginAttempts()
        throw new ApiError(400, '用户名或密码错误')
      }

      // 重置登录尝试次数
      await user.resetLoginAttempts()

      // 更新最后登录信息
      await user.update({
        lastLoginAt: new Date(),
        lastLoginIp: clientIp
      })

      // 生成JWT令牌
      const tokens = await this.generateTokens(user)

      // 清除验证码缓存
      if (loginType === 'code') {
        await redisUtils.del(`verification_code:${username}:2`)
      }

      logger.info(`User logged in: ${user.id} - ${user.username}`)

      res.json({
        code: 200,
        message: '登录成功',
        data: {
          user: user.toJSON(),
          ...tokens
        }
      })
    } catch (error) {
      next(error)
    }
  }

  // 发送验证码
  async sendVerificationCode(req, res, next) {
    try {
      const { target, type } = req.body // type: 1-注册, 2-登录, 3-重置密码

      // 生成6位随机验证码
      const code = Math.random().toString().slice(-6)

      // 检查发送频率限制
      const rateLimitKey = `code_rate_limit:${target}`
      const lastSent = await redisUtils.get(rateLimitKey)
      if (lastSent) {
        throw new ApiError(400, '验证码发送过于频繁，请稍后再试')
      }

      // 存储验证码，有效期5分钟
      const cacheKey = `verification_code:${target}:${type}`
      await redisUtils.set(cacheKey, code, 300)

      // 设置发送频率限制，60秒内不能重复发送
      await redisUtils.set(rateLimitKey, Date.now(), 60)

      // 判断是手机号还是邮箱
      const isPhone = /^1[3-9]\d{9}$/.test(target)
      const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(target)

      if (isPhone) {
        // 发送短信验证码
        await smsService.sendVerificationCode(target, code)
      } else if (isEmail) {
        // 发送邮件验证码
        await emailService.sendVerificationCode(target, code)
      } else {
        throw new ApiError(400, '请输入有效的手机号或邮箱')
      }

      logger.info(`Verification code sent to: ${target}`)

      res.json({
        code: 200,
        message: '验证码已发送',
        data: {
          target,
          expiresIn: 300
        }
      })
    } catch (error) {
      next(error)
    }
  }

  // 验证邮箱
  async verifyEmail(req, res, next) {
    try {
      const { token } = req.params

      const user = await User.findOne({
        where: { emailVerificationToken: token }
      })

      if (!user) {
        throw new ApiError(400, '验证链接无效或已过期')
      }

      await user.update({
        emailVerified: true,
        emailVerificationToken: null
      })

      logger.info(`Email verified for user: ${user.id} - ${user.email}`)

      res.json({
        code: 200,
        message: '邮箱验证成功',
        data: null
      })
    } catch (error) {
      next(error)
    }
  }

  // 忘记密码
  async forgotPassword(req, res, next) {
    try {
      const { email } = req.body

      const user = await User.findOne({ where: { email } })
      if (!user) {
        // 为了安全，即使用户不存在也返回成功
        return res.json({
          code: 200,
          message: '如果该邮箱已注册，重置链接已发送',
          data: null
        })
      }

      // 生成重置令牌
      const resetToken = crypto.randomBytes(32).toString('hex')
      const resetExpires = new Date(Date.now() + 3600000) // 1小时后过期

      await user.update({
        passwordResetToken: resetToken,
        passwordResetExpires: resetExpires
      })

      // 发送重置邮件
      await emailService.sendPasswordResetEmail(email, resetToken)

      logger.info(`Password reset requested for user: ${user.id} - ${user.email}`)

      res.json({
        code: 200,
        message: '如果该邮箱已注册，重置链接已发送',
        data: null
      })
    } catch (error) {
      next(error)
    }
  }

  // 重置密码
  async resetPassword(req, res, next) {
    try {
      const { token, password } = req.body

      const user = await User.findOne({
        where: {
          passwordResetToken: token,
          passwordResetExpires: {
            [User.sequelize.Sequelize.Op.gt]: new Date()
          }
        }
      })

      if (!user) {
        throw new ApiError(400, '重置链接无效或已过期')
      }

      await user.update({
        password,
        passwordResetToken: null,
        passwordResetExpires: null,
        loginAttempts: 0,
        lockUntil: null
      })

      logger.info(`Password reset for user: ${user.id} - ${user.email}`)

      res.json({
        code: 200,
        message: '密码重置成功',
        data: null
      })
    } catch (error) {
      next(error)
    }
  }

  // 刷新令牌
  async refreshToken(req, res, next) {
    try {
      const { refreshToken } = req.body

      if (!refreshToken) {
        throw new ApiError(400, '刷新令牌不能为空')
      }

      // 验证刷新令牌
      const decoded = jwt.verify(refreshToken, config.jwt.secret)
      const user = await User.findActiveById(decoded.userId)

      if (!user) {
        throw new ApiError(401, '用户不存在或已被禁用')
      }

      // 检查令牌是否在黑名单中
      const isBlacklisted = await redisUtils.exists(`blacklist:${refreshToken}`)
      if (isBlacklisted) {
        throw new ApiError(401, '令牌已失效')
      }

      // 生成新的令牌对
      const tokens = await this.generateTokens(user)

      // 将旧的刷新令牌加入黑名单
      await redisUtils.set(`blacklist:${refreshToken}`, true, 30 * 24 * 60 * 60) // 30天

      res.json({
        code: 200,
        message: '令牌刷新成功',
        data: tokens
      })
    } catch (error) {
      if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
        next(new ApiError(401, '刷新令牌无效或已过期'))
      } else {
        next(error)
      }
    }
  }

  // 退出登录
  async logout(req, res, next) {
    try {
      const token = req.headers.authorization?.replace('Bearer ', '')
      const { refreshToken } = req.body

      // 将令牌加入黑名单
      if (token) {
        await redisUtils.set(`blacklist:${token}`, true, 7 * 24 * 60 * 60) // 7天
      }
      if (refreshToken) {
        await redisUtils.set(`blacklist:${refreshToken}`, true, 30 * 24 * 60 * 60) // 30天
      }

      res.json({
        code: 200,
        message: '退出登录成功',
        data: null
      })
    } catch (error) {
      next(error)
    }
  }

  // 生成JWT令牌
  async generateTokens(user) {
    const payload = {
      userId: user.id,
      username: user.username,
      role: user.role
    }

    const accessToken = jwt.sign(payload, config.jwt.secret, {
      expiresIn: config.jwt.expiresIn
    })

    const refreshToken = jwt.sign(payload, config.jwt.secret, {
      expiresIn: config.jwt.refreshExpiresIn
    })

    return {
      token: accessToken,
      refreshToken,
      expiresIn: config.jwt.expiresIn
    }
  }

  // 验证验证码
  async verifyCode(target, code, type) {
    const cacheKey = `verification_code:${target}:${type}`
    const storedCode = await redisUtils.get(cacheKey)
    return storedCode === code
  }
}

module.exports = new AuthController()
