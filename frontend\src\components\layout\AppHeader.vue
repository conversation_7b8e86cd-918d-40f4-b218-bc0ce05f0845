<template>
  <header class="app-header" :class="{ 'scrolled': isScrolled }">
    <div class="container">
      <div class="header-content">
        <!-- Logo -->
        <router-link to="/" class="logo">
          <img src="/logo.svg" alt="Materials Studio答疑平台" class="logo-image" />
          <span class="logo-text">Materials Studio答疑平台</span>
        </router-link>

        <!-- 导航菜单 -->
        <nav class="nav-menu" :class="{ 'mobile-open': isMobileMenuOpen }">
          <router-link 
            v-for="item in menuItems" 
            :key="item.path"
            :to="item.path" 
            class="nav-item"
            @click="closeMobileMenu"
          >
            <el-icon><component :is="item.icon" /></el-icon>
            {{ item.name }}
          </router-link>
        </nav>

        <!-- 搜索框 -->
        <div class="search-box hidden-mobile">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索问题..."
            @keyup.enter="handleSearch"
            class="search-input"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <!-- 用户操作区 -->
        <div class="user-actions">
          <!-- 未登录状态 -->
          <template v-if="!userStore.isAuthenticated">
            <el-button @click="$router.push('/login')" type="primary" link>
              登录
            </el-button>
            <el-button @click="$router.push('/register')" type="primary">
              注册
            </el-button>
          </template>

          <!-- 已登录状态 -->
          <template v-else>
            <!-- 通知 -->
            <el-badge :value="notificationCount" :hidden="notificationCount === 0">
              <el-button circle @click="showNotifications">
                <el-icon><Bell /></el-icon>
              </el-button>
            </el-badge>

            <!-- 用户菜单 -->
            <el-dropdown @command="handleUserCommand" trigger="click">
              <div class="user-info">
                <el-avatar :src="userStore.user?.avatar" :size="32">
                  {{ userStore.user?.username?.charAt(0) }}
                </el-avatar>
                <span class="username hidden-mobile">{{ userStore.user?.username }}</span>
                <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">
                    <el-icon><User /></el-icon>
                    个人中心
                  </el-dropdown-item>
                  <el-dropdown-item command="membership" v-if="!userStore.isMember">
                    <el-icon><Crown /></el-icon>
                    升级会员
                  </el-dropdown-item>
                  <el-dropdown-item command="settings">
                    <el-icon><Setting /></el-icon>
                    设置
                  </el-dropdown-item>
                  <el-dropdown-item command="admin" v-if="userStore.isAdmin">
                    <el-icon><Tools /></el-icon>
                    管理后台
                  </el-dropdown-item>
                  <el-dropdown-item divided command="logout">
                    <el-icon><SwitchButton /></el-icon>
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>

          <!-- 移动端菜单按钮 -->
          <el-button 
            class="mobile-menu-btn hidden-desktop"
            @click="toggleMobileMenu"
            circle
          >
            <el-icon><Menu /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 移动端搜索 -->
    <div class="mobile-search hidden-desktop" v-if="showMobileSearch">
      <div class="container">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索问题..."
          @keyup.enter="handleSearch"
          size="large"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
          <template #suffix>
            <el-button @click="showMobileSearch = false" link>
              取消
            </el-button>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 通知抽屉 -->
    <el-drawer
      v-model="notificationDrawer"
      title="消息通知"
      direction="rtl"
      size="400px"
    >
      <NotificationList @close="notificationDrawer = false" />
    </el-drawer>
  </header>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import NotificationList from '@/components/notifications/NotificationList.vue'
import {
  Search,
  Bell,
  User,
  Crown,
  Setting,
  Tools,
  SwitchButton,
  ArrowDown,
  Menu,
  Home,
  QuestionFilled,
  Document,
  Star
} from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const isScrolled = ref(false)
const isMobileMenuOpen = ref(false)
const showMobileSearch = ref(false)
const notificationDrawer = ref(false)
const searchKeyword = ref('')
const notificationCount = ref(0)

// 菜单项
const menuItems = ref([
  { name: '首页', path: '/', icon: Home },
  { name: '问题广场', path: '/questions', icon: QuestionFilled },
  { name: '资源库', path: '/resources', icon: Document },
  { name: '关于我们', path: '/about', icon: Star }
])

// 滚动监听
const handleScroll = () => {
  isScrolled.value = window.scrollY > 50
}

// 移动端菜单控制
const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}

const closeMobileMenu = () => {
  isMobileMenuOpen.value = false
}

// 搜索处理
const handleSearch = () => {
  if (searchKeyword.value.trim()) {
    router.push({
      path: '/questions',
      query: { keyword: searchKeyword.value.trim() }
    })
    showMobileSearch.value = false
  }
}

// 显示通知
const showNotifications = () => {
  notificationDrawer.value = true
}

// 用户菜单命令处理
const handleUserCommand = async (command) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'membership':
      router.push('/membership')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'admin':
      router.push('/admin')
      break
    case 'logout':
      await userStore.logout()
      router.push('/')
      break
  }
}

// 生命周期
onMounted(() => {
  window.addEventListener('scroll', handleScroll)
  // 获取通知数量
  // fetchNotificationCount()
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style lang="scss" scoped>
.app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  transition: all var(--transition-normal);
  
  &.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-md);
  }
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  gap: 24px;
}

// Logo
.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  text-decoration: none;
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1.125rem;
  
  .logo-image {
    width: 32px;
    height: 32px;
  }
  
  .logo-text {
    @media (max-width: 768px) {
      display: none;
    }
  }
}

// 导航菜单
.nav-menu {
  display: flex;
  align-items: center;
  gap: 32px;
  
  @media (max-width: 768px) {
    position: fixed;
    top: 80px;
    left: 0;
    right: 0;
    background: var(--bg-primary);
    flex-direction: column;
    padding: 24px;
    box-shadow: var(--shadow-lg);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    
    &.mobile-open {
      transform: translateY(0);
      opacity: 1;
      visibility: visible;
    }
  }
  
  .nav-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: var(--radius-md);
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    transition: all var(--transition-fast);
    
    &:hover,
    &.router-link-active {
      color: var(--primary-color);
      background: rgba(102, 126, 234, 0.1);
    }
    
    .el-icon {
      font-size: 16px;
    }
  }
}

// 搜索框
.search-box {
  flex: 1;
  max-width: 400px;
  
  .search-input {
    :deep(.el-input__wrapper) {
      border-radius: 24px;
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid rgba(255, 255, 255, 0.3);
      
      &:hover,
      &.is-focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
      }
    }
  }
}

// 用户操作区
.user-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: background var(--transition-fast);
  
  &:hover {
    background: rgba(102, 126, 234, 0.1);
  }
  
  .username {
    font-weight: 500;
    color: var(--text-primary);
  }
  
  .dropdown-icon {
    font-size: 12px;
    color: var(--text-light);
  }
}

.mobile-menu-btn {
  background: transparent;
  border: 1px solid var(--border-color);
}

// 移动端搜索
.mobile-search {
  padding: 16px 0;
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
}

// 响应式隐藏类
.hidden-mobile {
  @media (max-width: 768px) {
    display: none !important;
  }
}

.hidden-desktop {
  @media (min-width: 769px) {
    display: none !important;
  }
}

// 通知徽章
:deep(.el-badge__content) {
  background: var(--error-color);
  border: 2px solid var(--bg-primary);
}
</style>
