# 环境配置
NODE_ENV=production

# 端口配置
FRONTEND_PORT=3000
BACKEND_PORT=3001
HTTP_PORT=80
HTTPS_PORT=443

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=msqa
DB_PASSWORD=msqa123456
DB_DATABASE=materials_studio_qa
DB_ROOT_PASSWORD=root123456

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT配置
JWT_SECRET=materials-studio-qa-secret-key-change-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# 加密配置
BCRYPT_SALT_ROUNDS=12

# 邮件配置
EMAIL_HOST=smtp.qq.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-email-password
EMAIL_FROM=<EMAIL>

# 短信配置 (阿里云)
SMS_ACCESS_KEY_ID=your-access-key-id
SMS_ACCESS_KEY_SECRET=your-access-key-secret
SMS_SIGN_NAME=Materials Studio答疑平台
SMS_TEMPLATE_CODE=SMS_123456789

# 文件上传配置
UPLOAD_MAX_FILE_SIZE=10485760
UPLOAD_DIR=src/uploads
UPLOAD_BASE_URL=http://localhost:3001/uploads

# 前端配置
VITE_API_BASE_URL=http://localhost:3001/api

# 支付配置 (支付宝)
ALIPAY_APP_ID=your-alipay-app-id
ALIPAY_PRIVATE_KEY=your-alipay-private-key
ALIPAY_PUBLIC_KEY=your-alipay-public-key
ALIPAY_GATEWAY=https://openapi.alipay.com/gateway.do

# 支付配置 (微信)
WECHAT_APP_ID=your-wechat-app-id
WECHAT_MCH_ID=your-wechat-mch-id
WECHAT_KEY=your-wechat-key
WECHAT_CERT_PATH=path/to/wechat/cert

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d

# 第三方服务配置 (七牛云)
QINIU_ACCESS_KEY=your-qiniu-access-key
QINIU_SECRET_KEY=your-qiniu-secret-key
QINIU_BUCKET=your-qiniu-bucket
QINIU_DOMAIN=your-qiniu-domain

# Elasticsearch配置 (可选)
ES_HOST=localhost:9200
ES_INDEX=ms_qa
ES_PORT=9200

# Kibana配置 (可选)
KIBANA_PORT=5601

# 监控配置 (可选)
PROMETHEUS_PORT=9090
GRAFANA_PORT=3003
GRAFANA_PASSWORD=admin123456

# SSL证书配置 (生产环境)
SSL_CERT_PATH=/etc/ssl/certs/ms-qa.crt
SSL_KEY_PATH=/etc/ssl/private/ms-qa.key

# 域名配置 (生产环境)
DOMAIN=ms-qa.com
FRONTEND_URL=https://ms-qa.com
BACKEND_URL=https://api.ms-qa.com

# CDN配置 (可选)
CDN_URL=https://cdn.ms-qa.com

# 安全配置
CORS_ORIGIN=http://localhost:3000
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100

# 缓存配置
CACHE_DEFAULT_TTL=3600
CACHE_SHORT_TTL=300
CACHE_LONG_TTL=86400

# 会员配置
MEMBERSHIP_BASIC_MONTHLY=29
MEMBERSHIP_BASIC_YEARLY=298
MEMBERSHIP_PREMIUM_MONTHLY=99
MEMBERSHIP_PREMIUM_YEARLY=998

# 积分配置
POINTS_QUESTION=10
POINTS_ANSWER=20
POINTS_ACCEPTED_ANSWER=50
POINTS_DAILY_LOGIN=5

# 通知配置
NOTIFICATION_EMAIL_ENABLED=true
NOTIFICATION_SMS_ENABLED=false
NOTIFICATION_PUSH_ENABLED=true

# 搜索配置
SEARCH_RESULTS_PER_PAGE=20
SEARCH_MAX_RESULTS=1000

# 文件存储配置
STORAGE_TYPE=local
STORAGE_LOCAL_PATH=uploads
STORAGE_CLOUD_BUCKET=ms-qa-files
STORAGE_CLOUD_REGION=cn-beijing

# 备份配置
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=/backups

# 性能配置
DB_POOL_MAX=10
DB_POOL_MIN=0
DB_POOL_ACQUIRE=30000
DB_POOL_IDLE=10000

REDIS_POOL_MAX=10
REDIS_POOL_MIN=2

# 开发配置
DEBUG=false
MOCK_PAYMENT=false
MOCK_SMS=false
MOCK_EMAIL=false

# 测试配置
TEST_DB_DATABASE=materials_studio_qa_test
TEST_REDIS_DB=1

# 社交登录配置 (可选)
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

WECHAT_LOGIN_APP_ID=your-wechat-login-app-id
WECHAT_LOGIN_APP_SECRET=your-wechat-login-app-secret

# API限制配置
API_RATE_LIMIT_REQUESTS=1000
API_RATE_LIMIT_WINDOW=3600000

# 内容审核配置
CONTENT_MODERATION_ENABLED=true
CONTENT_MODERATION_API_KEY=your-moderation-api-key

# 统计分析配置
ANALYTICS_ENABLED=true
ANALYTICS_TRACKING_ID=your-analytics-tracking-id

# 错误报告配置
ERROR_REPORTING_ENABLED=true
ERROR_REPORTING_DSN=your-error-reporting-dsn
