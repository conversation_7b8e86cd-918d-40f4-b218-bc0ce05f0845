const { validationResult } = require('express-validator')
const { ValidationError } = require('../utils/errors')

// 验证请求中间件
const validateRequest = (req, res, next) => {
  const errors = validationResult(req)
  
  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      field: error.path || error.param,
      message: error.msg,
      value: error.value,
      location: error.location
    }))
    
    const error = new ValidationError('请求数据验证失败', formattedErrors)
    return next(error)
  }
  
  next()
}

// 自定义验证器
const customValidators = {
  // 验证用户名格式
  isValidUsername: (value) => {
    const usernameRegex = /^[a-zA-Z0-9_]{3,50}$/
    return usernameRegex.test(value)
  },

  // 验证密码强度
  isStrongPassword: (value) => {
    // 至少8位，包含大小写字母、数字
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/
    return passwordRegex.test(value)
  },

  // 验证中国手机号
  isChinesePhone: (value) => {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(value)
  },

  // 验证邮箱格式
  isValidEmail: (value) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(value)
  },

  // 验证URL格式
  isValidUrl: (value) => {
    try {
      new URL(value)
      return true
    } catch {
      return false
    }
  },

  // 验证日期格式
  isValidDate: (value) => {
    const date = new Date(value)
    return date instanceof Date && !isNaN(date)
  },

  // 验证JSON格式
  isValidJSON: (value) => {
    try {
      JSON.parse(value)
      return true
    } catch {
      return false
    }
  },

  // 验证标签数组
  isValidTags: (value) => {
    if (!Array.isArray(value)) return false
    if (value.length > 10) return false // 最多10个标签
    
    return value.every(tag => 
      typeof tag === 'string' && 
      tag.length >= 1 && 
      tag.length <= 20 &&
      /^[\u4e00-\u9fa5a-zA-Z0-9_-]+$/.test(tag) // 中文、英文、数字、下划线、连字符
    )
  },

  // 验证文件类型
  isValidFileType: (mimetype, allowedTypes) => {
    return allowedTypes.includes(mimetype)
  },

  // 验证文件大小
  isValidFileSize: (size, maxSize) => {
    return size <= maxSize
  },

  // 验证分页参数
  isValidPagination: (page, limit) => {
    const pageNum = parseInt(page)
    const limitNum = parseInt(limit)
    
    return pageNum > 0 && limitNum > 0 && limitNum <= 100
  },

  // 验证排序参数
  isValidSort: (sort, allowedFields) => {
    if (!sort) return true
    
    const [field, order] = sort.split(':')
    return allowedFields.includes(field) && ['asc', 'desc'].includes(order)
  },

  // 验证ID格式
  isValidId: (value) => {
    const id = parseInt(value)
    return Number.isInteger(id) && id > 0
  },

  // 验证枚举值
  isValidEnum: (value, enumValues) => {
    return enumValues.includes(value)
  },

  // 验证数组长度
  isValidArrayLength: (array, min, max) => {
    if (!Array.isArray(array)) return false
    return array.length >= min && array.length <= max
  },

  // 验证字符串长度
  isValidStringLength: (value, min, max) => {
    if (typeof value !== 'string') return false
    return value.length >= min && value.length <= max
  },

  // 验证数字范围
  isValidNumberRange: (value, min, max) => {
    const num = parseFloat(value)
    return !isNaN(num) && num >= min && num <= max
  },

  // 验证HTML内容（基础检查）
  isValidHtmlContent: (value) => {
    // 检查是否包含危险标签
    const dangerousTags = /<script|<iframe|<object|<embed|<form/i
    return !dangerousTags.test(value)
  },

  // 验证颜色值
  isValidColor: (value) => {
    const colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
    return colorRegex.test(value)
  },

  // 验证IP地址
  isValidIP: (value) => {
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/
    return ipv4Regex.test(value) || ipv6Regex.test(value)
  }
}

// 创建验证规则
const createValidationRules = {
  // 用户注册验证规则
  userRegistration: () => [
    body('username')
      .custom(customValidators.isValidUsername)
      .withMessage('用户名只能包含字母、数字和下划线，长度3-50位'),
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('请输入有效的邮箱地址'),
    body('password')
      .custom(customValidators.isStrongPassword)
      .withMessage('密码至少8位，必须包含大小写字母和数字'),
    body('phone')
      .optional()
      .custom(customValidators.isChinesePhone)
      .withMessage('请输入有效的手机号码')
  ],

  // 用户登录验证规则
  userLogin: () => [
    body('username')
      .notEmpty()
      .withMessage('请输入用户名、邮箱或手机号'),
    body('password')
      .if(body('loginType').equals('password'))
      .notEmpty()
      .withMessage('请输入密码'),
    body('code')
      .if(body('loginType').equals('code'))
      .isLength({ min: 6, max: 6 })
      .isNumeric()
      .withMessage('验证码必须是6位数字')
  ],

  // 问题发布验证规则
  questionCreation: () => [
    body('title')
      .isLength({ min: 5, max: 200 })
      .withMessage('问题标题长度必须在5-200个字符之间'),
    body('content')
      .isLength({ min: 10, max: 50000 })
      .custom(customValidators.isValidHtmlContent)
      .withMessage('问题内容长度必须在10-50000个字符之间，且不能包含危险标签'),
    body('categoryId')
      .optional()
      .custom(customValidators.isValidId)
      .withMessage('分类ID无效'),
    body('tags')
      .optional()
      .custom(customValidators.isValidTags)
      .withMessage('标签格式无效，最多10个标签，每个标签1-20个字符'),
    body('difficulty')
      .optional()
      .custom(value => customValidators.isValidEnum(value, ['beginner', 'intermediate', 'advanced', 'expert']))
      .withMessage('难度级别无效')
  ],

  // 回答发布验证规则
  answerCreation: () => [
    body('content')
      .isLength({ min: 10, max: 50000 })
      .custom(customValidators.isValidHtmlContent)
      .withMessage('回答内容长度必须在10-50000个字符之间，且不能包含危险标签'),
    param('questionId')
      .custom(customValidators.isValidId)
      .withMessage('问题ID无效')
  ],

  // 评论发布验证规则
  commentCreation: () => [
    body('content')
      .isLength({ min: 1, max: 1000 })
      .withMessage('评论内容长度必须在1-1000个字符之间'),
    body('targetType')
      .custom(value => customValidators.isValidEnum(value, ['question', 'answer']))
      .withMessage('评论目标类型无效'),
    body('targetId')
      .custom(customValidators.isValidId)
      .withMessage('评论目标ID无效')
  ],

  // 分页验证规则
  pagination: () => [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('页码必须是正整数'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('每页数量必须在1-100之间')
  ],

  // 文件上传验证规则
  fileUpload: (allowedTypes, maxSize) => [
    (req, res, next) => {
      if (!req.file) {
        return next(new ValidationError('请选择要上传的文件'))
      }

      if (!customValidators.isValidFileType(req.file.mimetype, allowedTypes)) {
        return next(new ValidationError('文件类型不支持'))
      }

      if (!customValidators.isValidFileSize(req.file.size, maxSize)) {
        return next(new ValidationError('文件大小超出限制'))
      }

      next()
    }
  ]
}

// 导入body, query, param等验证器
const { body, query, param, check } = require('express-validator')

module.exports = {
  validateRequest,
  customValidators,
  createValidationRules,
  body,
  query,
  param,
  check
}
