import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import router from '@/router'

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证token
    const userStore = useUserStore()
    if (userStore.token) {
      config.headers.Authorization = `Bearer ${userStore.token}`
    }
    
    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }
    
    return config
  },
  (error) => {
    console.error('Request error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    const { code, message, data } = response.data
    
    // 检查业务状态码
    if (code !== 200) {
      ElMessage.error(message || '请求失败')
      return Promise.reject(new Error(message || '请求失败'))
    }
    
    return response
  },
  async (error) => {
    const { response } = error
    
    if (!response) {
      ElMessage.error('网络连接失败，请检查网络设置')
      return Promise.reject(error)
    }
    
    const { status, data } = response
    const userStore = useUserStore()
    
    switch (status) {
      case 401:
        // 未授权，尝试刷新token
        if (userStore.token && !error.config._retry) {
          error.config._retry = true
          try {
            await userStore.refreshToken()
            return api(error.config)
          } catch (refreshError) {
            userStore.logout()
            router.push('/login')
            ElMessage.error('登录已过期，请重新登录')
          }
        } else {
          userStore.logout()
          router.push('/login')
          ElMessage.error('请先登录')
        }
        break
        
      case 403:
        ElMessage.error('权限不足')
        break
        
      case 404:
        ElMessage.error('请求的资源不存在')
        break
        
      case 429:
        ElMessage.error('请求过于频繁，请稍后再试')
        break
        
      case 500:
        ElMessage.error('服务器内部错误')
        break
        
      default:
        ElMessage.error(data?.message || `请求失败 (${status})`)
    }
    
    return Promise.reject(error)
  }
)

// 文件上传方法
export const uploadFile = async (file, onProgress) => {
  const formData = new FormData()
  formData.append('file', file)
  
  return api.post('/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress) {
        const percentCompleted = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total
        )
        onProgress(percentCompleted)
      }
    }
  })
}

// 下载文件方法
export const downloadFile = async (url, filename) => {
  try {
    const response = await api.get(url, {
      responseType: 'blob'
    })
    
    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
    
    ElMessage.success('下载成功')
  } catch (error) {
    ElMessage.error('下载失败')
    throw error
  }
}

// 批量请求方法
export const batchRequest = async (requests) => {
  try {
    const responses = await Promise.allSettled(requests)
    return responses.map((response, index) => ({
      index,
      success: response.status === 'fulfilled',
      data: response.status === 'fulfilled' ? response.value.data : null,
      error: response.status === 'rejected' ? response.reason : null
    }))
  } catch (error) {
    console.error('Batch request error:', error)
    throw error
  }
}

// 取消请求的方法
export const createCancelToken = () => {
  return axios.CancelToken.source()
}

// 检查请求是否被取消
export const isCancel = axios.isCancel

export default api
