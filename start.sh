#!/bin/bash

# Materials Studio答疑平台启动脚本
# 用于快速启动开发环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${2}${1}${NC}"
}

print_success() {
    print_message "$1" "$GREEN"
}

print_error() {
    print_message "$1" "$RED"
}

print_warning() {
    print_message "$1" "$YELLOW"
}

print_info() {
    print_message "$1" "$BLUE"
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 检查端口是否被占用
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# 等待服务启动
wait_for_service() {
    local host=$1
    local port=$2
    local service_name=$3
    local max_attempts=30
    local attempt=1

    print_info "等待 $service_name 启动..."
    
    while [ $attempt -le $max_attempts ]; do
        if nc -z $host $port 2>/dev/null; then
            print_success "$service_name 已启动"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name 启动超时"
    return 1
}

# 显示帮助信息
show_help() {
    echo "Materials Studio答疑平台启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  dev         启动开发环境"
    echo "  prod        启动生产环境"
    echo "  stop        停止所有服务"
    echo "  restart     重启所有服务"
    echo "  logs        查看日志"
    echo "  status      查看服务状态"
    echo "  clean       清理数据和容器"
    echo "  install     安装依赖"
    echo "  help        显示此帮助信息"
    echo ""
}

# 检查依赖
check_dependencies() {
    print_info "检查依赖..."
    
    local missing_deps=()
    
    if ! command_exists node; then
        missing_deps+=("Node.js")
    fi
    
    if ! command_exists npm; then
        missing_deps+=("npm")
    fi
    
    if ! command_exists docker; then
        missing_deps+=("Docker")
    fi
    
    if ! command_exists docker-compose; then
        missing_deps+=("Docker Compose")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        print_error "缺少以下依赖:"
        for dep in "${missing_deps[@]}"; do
            echo "  - $dep"
        done
        print_error "请安装缺少的依赖后重试"
        exit 1
    fi
    
    print_success "依赖检查通过"
}

# 安装依赖
install_dependencies() {
    print_info "安装项目依赖..."
    
    # 安装后端依赖
    if [ -d "backend" ]; then
        print_info "安装后端依赖..."
        cd backend
        npm install
        cd ..
        print_success "后端依赖安装完成"
    fi
    
    # 安装前端依赖
    if [ -d "frontend" ]; then
        print_info "安装前端依赖..."
        cd frontend
        npm install
        cd ..
        print_success "前端依赖安装完成"
    fi
    
    print_success "所有依赖安装完成"
}

# 创建环境配置文件
create_env_file() {
    if [ ! -f ".env" ]; then
        print_info "创建环境配置文件..."
        cp .env.example .env
        print_warning "请编辑 .env 文件配置您的环境变量"
    fi
}

# 启动开发环境
start_dev() {
    print_info "启动开发环境..."
    
    check_dependencies
    create_env_file
    
    # 检查端口占用
    if check_port 3000; then
        print_warning "端口 3000 已被占用，前端可能无法启动"
    fi
    
    if check_port 3001; then
        print_warning "端口 3001 已被占用，后端可能无法启动"
    fi
    
    # 启动数据库和Redis
    print_info "启动数据库和缓存服务..."
    docker-compose up -d mysql redis
    
    # 等待数据库启动
    wait_for_service localhost 3306 "MySQL"
    wait_for_service localhost 6379 "Redis"
    
    # 启动后端
    print_info "启动后端服务..."
    cd backend
    npm run dev &
    BACKEND_PID=$!
    cd ..
    
    # 等待后端启动
    sleep 5
    wait_for_service localhost 3001 "后端API"
    
    # 启动前端
    print_info "启动前端服务..."
    cd frontend
    npm run serve &
    FRONTEND_PID=$!
    cd ..
    
    # 等待前端启动
    wait_for_service localhost 3000 "前端应用"
    
    print_success "开发环境启动完成!"
    print_info "访问地址:"
    print_info "  前端应用: http://localhost:3000"
    print_info "  后端API: http://localhost:3001"
    print_info "  API文档: http://localhost:3001/api-docs"
    print_info ""
    print_info "按 Ctrl+C 停止服务"
    
    # 等待用户中断
    trap 'kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; docker-compose stop mysql redis; exit' INT
    wait
}

# 启动生产环境
start_prod() {
    print_info "启动生产环境..."
    
    check_dependencies
    create_env_file
    
    # 构建并启动所有服务
    docker-compose up -d --build
    
    # 等待服务启动
    wait_for_service localhost 3306 "MySQL"
    wait_for_service localhost 6379 "Redis"
    wait_for_service localhost 3001 "后端API"
    wait_for_service localhost 80 "前端应用"
    
    print_success "生产环境启动完成!"
    print_info "访问地址: http://localhost"
}

# 停止服务
stop_services() {
    print_info "停止所有服务..."
    
    # 停止Docker服务
    docker-compose down
    
    # 停止本地进程
    pkill -f "npm run dev" 2>/dev/null || true
    pkill -f "npm run serve" 2>/dev/null || true
    
    print_success "所有服务已停止"
}

# 重启服务
restart_services() {
    print_info "重启服务..."
    stop_services
    sleep 2
    start_prod
}

# 查看日志
show_logs() {
    print_info "显示服务日志..."
    docker-compose logs -f
}

# 查看服务状态
show_status() {
    print_info "服务状态:"
    docker-compose ps
    
    print_info ""
    print_info "端口占用情况:"
    
    ports=(3000 3001 3306 6379 80 443)
    for port in "${ports[@]}"; do
        if check_port $port; then
            print_success "端口 $port: 已占用"
        else
            print_warning "端口 $port: 空闲"
        fi
    done
}

# 清理数据和容器
clean_all() {
    print_warning "这将删除所有容器、镜像和数据卷，确定要继续吗? (y/N)"
    read -r response
    
    if [[ "$response" =~ ^[Yy]$ ]]; then
        print_info "清理所有数据..."
        docker-compose down -v --rmi all --remove-orphans
        docker system prune -f
        print_success "清理完成"
    else
        print_info "取消清理操作"
    fi
}

# 主函数
main() {
    case "${1:-help}" in
        "dev")
            start_dev
            ;;
        "prod")
            start_prod
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "logs")
            show_logs
            ;;
        "status")
            show_status
            ;;
        "clean")
            clean_all
            ;;
        "install")
            install_dependencies
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 运行主函数
main "$@"
