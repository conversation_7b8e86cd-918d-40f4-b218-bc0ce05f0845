<template>
  <div class="home">
    <!-- 导航栏 -->
    <AppHeader />
    
    <!-- 主要内容 -->
    <main class="main-content">
      <!-- 英雄区域 -->
      <section class="hero-section">
        <div class="container">
          <div class="hero-content">
            <h1 class="hero-title">
              Materials Studio
              <span class="gradient-text">答疑平台</span>
            </h1>
            <p class="hero-subtitle">
              专业的分子模拟学习与问答社区，为研究生、博士生提供高质量的技术支持
            </p>
            <div class="hero-actions">
              <el-button 
                type="primary" 
                size="large" 
                @click="$router.push('/questions')"
                class="btn-gradient"
              >
                <el-icon><Search /></el-icon>
                浏览问题
              </el-button>
              <el-button 
                size="large" 
                @click="$router.push('/ask')"
                v-if="userStore.isAuthenticated"
              >
                <el-icon><Edit /></el-icon>
                我要提问
              </el-button>
              <el-button 
                size="large" 
                @click="$router.push('/register')"
                v-else
              >
                <el-icon><User /></el-icon>
                立即注册
              </el-button>
            </div>
          </div>
          <div class="hero-image">
            <div class="floating-cards">
              <div class="card card-1">
                <el-icon><Document /></el-icon>
                <span>专业问答</span>
              </div>
              <div class="card card-2">
                <el-icon><VideoPlay /></el-icon>
                <span>视频教程</span>
              </div>
              <div class="card card-3">
                <el-icon><Download /></el-icon>
                <span>资源下载</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 功能特色 -->
      <section class="features-section">
        <div class="container">
          <h2 class="section-title">平台特色</h2>
          <div class="features-grid">
            <div class="feature-card" v-for="feature in features" :key="feature.id">
              <div class="feature-icon">
                <component :is="feature.icon" />
              </div>
              <h3 class="feature-title">{{ feature.title }}</h3>
              <p class="feature-description">{{ feature.description }}</p>
            </div>
          </div>
        </div>
      </section>

      <!-- 热门问题 -->
      <section class="popular-questions-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">热门问题</h2>
            <el-button 
              type="primary" 
              link 
              @click="$router.push('/questions')"
            >
              查看更多
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </div>
          
          <div class="questions-grid" v-loading="isLoading">
            <QuestionCard 
              v-for="question in popularQuestions" 
              :key="question.id"
              :question="question"
              @click="$router.push(`/questions/${question.id}`)"
            />
          </div>
        </div>
      </section>

      <!-- 会员权益 -->
      <section class="membership-section">
        <div class="container">
          <h2 class="section-title">会员权益</h2>
          <div class="membership-grid">
            <div class="membership-card" v-for="plan in membershipPlans" :key="plan.id">
              <div class="plan-header">
                <h3 class="plan-name">{{ plan.name }}</h3>
                <div class="plan-price">
                  <span class="currency">¥</span>
                  <span class="amount">{{ plan.price }}</span>
                  <span class="period">/{{ plan.period }}</span>
                </div>
              </div>
              <ul class="plan-features">
                <li v-for="feature in plan.features" :key="feature">
                  <el-icon class="check-icon"><Check /></el-icon>
                  {{ feature }}
                </li>
              </ul>
              <el-button 
                :type="plan.recommended ? 'primary' : 'default'"
                class="plan-button"
                @click="$router.push('/membership')"
              >
                {{ plan.recommended ? '立即升级' : '了解详情' }}
              </el-button>
            </div>
          </div>
        </div>
      </section>

      <!-- 专家团队 -->
      <section class="experts-section">
        <div class="container">
          <h2 class="section-title">专家团队</h2>
          <div class="experts-grid">
            <div class="expert-card" v-for="expert in experts" :key="expert.id">
              <div class="expert-avatar">
                <img :src="expert.avatar" :alt="expert.name" />
              </div>
              <h3 class="expert-name">{{ expert.name }}</h3>
              <p class="expert-title">{{ expert.title }}</p>
              <p class="expert-description">{{ expert.description }}</p>
              <div class="expert-stats">
                <span>回答 {{ expert.answerCount }}</span>
                <span>获赞 {{ expert.likeCount }}</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- 页脚 -->
    <AppFooter />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { useQuestionsStore } from '@/stores/questions'
import AppHeader from '@/components/layout/AppHeader.vue'
import AppFooter from '@/components/layout/AppFooter.vue'
import QuestionCard from '@/components/questions/QuestionCard.vue'
import {
  Search,
  Edit,
  User,
  Document,
  VideoPlay,
  Download,
  ArrowRight,
  Check,
  Star,
  Trophy,
  Shield,
  Cpu
} from '@element-plus/icons-vue'

const userStore = useUserStore()
const questionsStore = useQuestionsStore()

const isLoading = ref(false)
const popularQuestions = ref([])

// 功能特色数据
const features = ref([
  {
    id: 1,
    icon: Star,
    title: '专业垂直',
    description: '专注Materials Studio及分子模拟领域，提供最专业的技术支持'
  },
  {
    id: 2,
    icon: Trophy,
    title: '分级会员',
    description: '基础用户、初级会员、高级会员权限体系，满足不同需求'
  },
  {
    id: 3,
    icon: VideoPlay,
    title: '多媒体答疑',
    description: '支持文字、图片、视频等多种形式，让学习更直观'
  },
  {
    id: 4,
    icon: Shield,
    title: '质量保证',
    description: '专家认证体系，确保回答的专业性和准确性'
  }
])

// 会员套餐数据
const membershipPlans = ref([
  {
    id: 1,
    name: '基础用户',
    price: 0,
    period: '永久',
    features: ['浏览基础问答', '发布问题', '基础搜索'],
    recommended: false
  },
  {
    id: 2,
    name: '初级会员',
    price: 29,
    period: '月',
    features: ['查看完整回答', '视频教程访问', '资源下载', '优先回答'],
    recommended: true
  },
  {
    id: 3,
    name: '高级会员',
    price: 498,
    period: '年',
    features: ['所有初级权益', '专家一对一咨询', '高级资源', '无限下载'],
    recommended: false
  }
])

// 专家团队数据
const experts = ref([
  {
    id: 1,
    name: '张教授',
    title: '材料科学专家',
    description: '20年Materials Studio使用经验，专注高分子材料模拟',
    avatar: '/images/expert1.jpg',
    answerCount: 156,
    likeCount: 892
  },
  {
    id: 2,
    name: '李博士',
    title: '计算化学专家',
    description: '精通DFT计算和分子动力学模拟，发表SCI论文50+',
    avatar: '/images/expert2.jpg',
    answerCount: 203,
    likeCount: 1205
  },
  {
    id: 3,
    name: '王研究员',
    title: '纳米材料专家',
    description: '专注纳米材料设计与性能预测，Materials Studio资深用户',
    avatar: '/images/expert3.jpg',
    answerCount: 89,
    likeCount: 567
  }
])

// 获取热门问题
const fetchPopularQuestions = async () => {
  try {
    isLoading.value = true
    await questionsStore.fetchQuestions({
      sort: 'view_count',
      order: 'desc',
      limit: 6
    })
    popularQuestions.value = questionsStore.questions
  } catch (error) {
    console.error('Failed to fetch popular questions:', error)
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  fetchPopularQuestions()
})
</script>

<style lang="scss" scoped>
.home {
  min-height: 100vh;
  background: var(--bg-gradient);
}

.main-content {
  padding-top: 80px; // 为固定导航栏留出空间
}

// 英雄区域
.hero-section {
  padding: 100px 0;
  position: relative;
  overflow: hidden;
  
  .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 40px;
      text-align: center;
    }
  }
  
  .hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--text-white);
    margin-bottom: 20px;
    line-height: 1.2;
    
    @media (max-width: 768px) {
      font-size: 2.5rem;
    }
  }
  
  .gradient-text {
    background: linear-gradient(45deg, #f093fb, #f5576c);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .hero-subtitle {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 40px;
    line-height: 1.6;
  }
  
  .hero-actions {
    display: flex;
    gap: 16px;
    
    @media (max-width: 768px) {
      flex-direction: column;
      align-items: center;
    }
  }
  
  .btn-gradient {
    background: linear-gradient(45deg, #f093fb, #f5576c);
    border: none;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(240, 147, 251, 0.3);
    }
  }
}

// 浮动卡片动画
.floating-cards {
  position: relative;
  height: 400px;
  
  .card {
    position: absolute;
    background: var(--bg-card);
    padding: 20px;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: 12px;
    backdrop-filter: blur(10px);
    animation: float 6s ease-in-out infinite;
    
    .el-icon {
      font-size: 24px;
      color: var(--primary-color);
    }
    
    span {
      font-weight: 500;
      color: var(--text-primary);
    }
    
    &.card-1 {
      top: 50px;
      left: 50px;
      animation-delay: 0s;
    }
    
    &.card-2 {
      top: 150px;
      right: 80px;
      animation-delay: 2s;
    }
    
    &.card-3 {
      bottom: 80px;
      left: 100px;
      animation-delay: 4s;
    }
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

// 通用区域样式
.features-section,
.popular-questions-section,
.membership-section,
.experts-section {
  padding: 80px 0;
  background: var(--bg-primary);
}

.section-title {
  font-size: 2.5rem;
  font-weight: 600;
  text-align: center;
  margin-bottom: 60px;
  color: var(--text-primary);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 60px;
  
  .section-title {
    margin-bottom: 0;
  }
}

// 功能特色
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
}

.feature-card {
  text-align: center;
  padding: 40px 20px;
  border-radius: var(--radius-lg);
  transition: transform var(--transition-normal);
  
  &:hover {
    transform: translateY(-10px);
  }
  
  .feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    background: var(--bg-gradient);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    
    .el-icon {
      font-size: 32px;
      color: var(--text-white);
    }
  }
  
  .feature-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-primary);
  }
  
  .feature-description {
    color: var(--text-secondary);
    line-height: 1.6;
  }
}

// 问题网格
.questions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
}

// 会员套餐
.membership-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
  max-width: 1000px;
  margin: 0 auto;
}

.membership-card {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: 40px 32px;
  text-align: center;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  
  &:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
  }
  
  .plan-header {
    margin-bottom: 32px;
  }
  
  .plan-name {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-primary);
  }
  
  .plan-price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 4px;
    
    .currency {
      font-size: 1.25rem;
      color: var(--text-secondary);
    }
    
    .amount {
      font-size: 3rem;
      font-weight: 700;
      color: var(--primary-color);
    }
    
    .period {
      font-size: 1rem;
      color: var(--text-secondary);
    }
  }
  
  .plan-features {
    list-style: none;
    margin-bottom: 32px;
    
    li {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 8px 0;
      
      .check-icon {
        color: var(--success-color);
        font-size: 16px;
      }
    }
  }
  
  .plan-button {
    width: 100%;
  }
}

// 专家团队
.experts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 32px;
}

.expert-card {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: 32px;
  text-align: center;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  
  &:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
  }
  
  .expert-avatar {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    border-radius: var(--radius-full);
    overflow: hidden;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  
  .expert-name {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--text-primary);
  }
  
  .expert-title {
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 16px;
  }
  
  .expert-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 20px;
  }
  
  .expert-stats {
    display: flex;
    justify-content: center;
    gap: 24px;
    
    span {
      color: var(--text-light);
      font-size: 0.875rem;
    }
  }
}
</style>
