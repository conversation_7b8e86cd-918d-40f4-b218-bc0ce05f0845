{"name": "materials-studio-platform-backend", "version": "1.0.0", "description": "Materials Studio答疑平台后端API服务", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/ --fix", "format": "prettier --write src/", "db:migrate": "node src/scripts/migrate.js", "db:seed": "node src/scripts/seed.js", "db:reset": "npm run db:migrate && npm run db:seed"}, "keywords": ["materials-studio", "qa-platform", "nodejs", "express", "mysql"], "author": "Materials Studio Platform Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "mysql2": "^3.6.0", "sequelize": "^6.32.1", "redis": "^4.6.7", "jsonwebtoken": "^9.0.1", "bcryptjs": "^2.4.3", "joi": "^17.9.2", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.4", "express-rate-limit": "^6.8.1", "express-validator": "^7.0.1", "moment": "^2.29.4", "uuid": "^9.0.0", "lodash": "^4.17.21", "axios": "^1.4.0", "sharp": "^0.32.4", "marked": "^5.1.1", "dompurify": "^3.0.3", "jsdom": "^22.1.0", "node-cron": "^3.0.2", "winston": "^3.10.0", "express-winston": "^4.2.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3", "eslint": "^8.45.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-node": "^11.1.0", "prettier": "^3.0.0", "@types/jest": "^29.5.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/app.js", "!src/config/**", "!src/scripts/**"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}, "nodemonConfig": {"watch": ["src"], "ext": "js,json", "ignore": ["src/uploads/**", "src/logs/**"], "env": {"NODE_ENV": "development"}}}