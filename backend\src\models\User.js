const { DataTypes } = require('sequelize')
const bcrypt = require('bcryptjs')
const { sequelize } = require('../config/database')
const config = require('../config')

const User = sequelize.define('User', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  username: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    validate: {
      len: [3, 50],
      notEmpty: true
    }
  },
  email: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true,
      notEmpty: true
    }
  },
  phone: {
    type: DataTypes.STRING(20),
    allowNull: true,
    unique: true,
    validate: {
      is: /^1[3-9]\d{9}$/
    }
  },
  password: {
    type: DataTypes.STRING(255),
    allowNull: false,
    validate: {
      len: [6, 255],
      notEmpty: true
    }
  },
  avatar: {
    type: DataTypes.STRING(500),
    allowNull: true,
    defaultValue: null
  },
  nickname: {
    type: DataTypes.STRING(50),
    allowNull: true,
    validate: {
      len: [1, 50]
    }
  },
  bio: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  gender: {
    type: DataTypes.ENUM('male', 'female', 'other'),
    allowNull: true,
    defaultValue: null
  },
  birthday: {
    type: DataTypes.DATEONLY,
    allowNull: true
  },
  location: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  website: {
    type: DataTypes.STRING(255),
    allowNull: true,
    validate: {
      isUrl: true
    }
  },
  role: {
    type: DataTypes.ENUM('user', 'expert', 'admin'),
    allowNull: false,
    defaultValue: 'user'
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive', 'banned'),
    allowNull: false,
    defaultValue: 'active'
  },
  memberLevel: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '0: 基础用户, 1: 初级会员, 2: 高级会员'
  },
  memberExpiredAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '会员到期时间'
  },
  emailVerified: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  phoneVerified: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  emailVerificationToken: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  passwordResetToken: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  passwordResetExpires: {
    type: DataTypes.DATE,
    allowNull: true
  },
  lastLoginAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  lastLoginIp: {
    type: DataTypes.STRING(45),
    allowNull: true
  },
  loginAttempts: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  lockUntil: {
    type: DataTypes.DATE,
    allowNull: true
  },
  points: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '用户积分'
  },
  reputation: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '用户声誉值'
  },
  questionCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '提问数量'
  },
  answerCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '回答数量'
  },
  acceptedAnswerCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '被采纳回答数量'
  },
  settings: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {
      emailNotifications: true,
      smsNotifications: false,
      privacy: {
        showEmail: false,
        showPhone: false,
        showLocation: true
      }
    }
  }
}, {
  tableName: 'users',
  indexes: [
    {
      fields: ['email']
    },
    {
      fields: ['phone']
    },
    {
      fields: ['username']
    },
    {
      fields: ['role']
    },
    {
      fields: ['status']
    },
    {
      fields: ['memberLevel']
    },
    {
      fields: ['reputation']
    },
    {
      fields: ['createdAt']
    }
  ]
})

// 密码加密钩子
User.beforeCreate(async (user) => {
  if (user.password) {
    user.password = await bcrypt.hash(user.password, config.bcrypt.saltRounds)
  }
})

User.beforeUpdate(async (user) => {
  if (user.changed('password')) {
    user.password = await bcrypt.hash(user.password, config.bcrypt.saltRounds)
  }
})

// 实例方法
User.prototype.validatePassword = async function(password) {
  return await bcrypt.compare(password, this.password)
}

User.prototype.isLocked = function() {
  return !!(this.lockUntil && this.lockUntil > Date.now())
}

User.prototype.incrementLoginAttempts = async function() {
  // 如果之前有锁定且已过期，重置计数器
  if (this.lockUntil && this.lockUntil < Date.now()) {
    return this.update({
      loginAttempts: 1,
      lockUntil: null
    })
  }
  
  const updates = { loginAttempts: this.loginAttempts + 1 }
  
  // 如果达到最大尝试次数，锁定账户
  if (this.loginAttempts + 1 >= config.security.maxLoginAttempts && !this.isLocked()) {
    updates.lockUntil = Date.now() + config.security.lockoutTime
  }
  
  return this.update(updates)
}

User.prototype.resetLoginAttempts = async function() {
  return this.update({
    loginAttempts: 0,
    lockUntil: null
  })
}

User.prototype.isMember = function() {
  return this.memberLevel > 0 && (!this.memberExpiredAt || this.memberExpiredAt > new Date())
}

User.prototype.canAccessFeature = function(feature) {
  const memberConfig = config.membership.levels[this.memberLevel]
  return memberConfig && memberConfig.features.includes(feature)
}

User.prototype.toJSON = function() {
  const values = { ...this.get() }
  delete values.password
  delete values.emailVerificationToken
  delete values.passwordResetToken
  delete values.loginAttempts
  delete values.lockUntil
  return values
}

// 类方法
User.findByEmailOrUsername = function(identifier) {
  return this.findOne({
    where: {
      [sequelize.Sequelize.Op.or]: [
        { email: identifier },
        { username: identifier },
        { phone: identifier }
      ]
    }
  })
}

User.findActiveById = function(id) {
  return this.findOne({
    where: {
      id,
      status: 'active'
    }
  })
}

module.exports = User
