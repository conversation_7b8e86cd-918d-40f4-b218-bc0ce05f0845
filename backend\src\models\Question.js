const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

const Question = sequelize.define('Question', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  title: {
    type: DataTypes.STRING(200),
    allowNull: false,
    validate: {
      len: [5, 200],
      notEmpty: true
    }
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: false,
    validate: {
      len: [10, 50000],
      notEmpty: true
    }
  },
  brief: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: '问题简介，用于列表展示'
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  categoryId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'categories',
      key: 'id'
    }
  },
  tags: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: [],
    comment: '问题标签数组'
  },
  status: {
    type: DataTypes.ENUM('draft', 'pending', 'published', 'closed', 'deleted'),
    allowNull: false,
    defaultValue: 'published'
  },
  priority: {
    type: DataTypes.ENUM('low', 'normal', 'high', 'urgent'),
    allowNull: false,
    defaultValue: 'normal'
  },
  difficulty: {
    type: DataTypes.ENUM('beginner', 'intermediate', 'advanced', 'expert'),
    allowNull: true,
    comment: '问题难度级别'
  },
  isTop: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否置顶'
  },
  isEssence: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否精华'
  },
  isAnonymous: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否匿名提问'
  },
  requireMembership: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '查看所需会员等级，0表示免费'
  },
  viewCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '浏览次数'
  },
  answerCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '回答数量'
  },
  favoriteCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '收藏次数'
  },
  likeCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '点赞次数'
  },
  dislikeCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '点踩次数'
  },
  score: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
    comment: '问题评分'
  },
  acceptedAnswerId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'answers',
      key: 'id'
    },
    comment: '被采纳的回答ID'
  },
  lastAnswerAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '最后回答时间'
  },
  lastAnswerUserId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: '最后回答用户ID'
  },
  closedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '关闭时间'
  },
  closedReason: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: '关闭原因'
  },
  attachments: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: [],
    comment: '附件列表'
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {},
    comment: '扩展元数据'
  },
  searchVector: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '搜索向量，用于全文搜索'
  }
}, {
  tableName: 'questions',
  indexes: [
    {
      fields: ['userId']
    },
    {
      fields: ['categoryId']
    },
    {
      fields: ['status']
    },
    {
      fields: ['priority']
    },
    {
      fields: ['isTop', 'isEssence']
    },
    {
      fields: ['requireMembership']
    },
    {
      fields: ['viewCount']
    },
    {
      fields: ['answerCount']
    },
    {
      fields: ['score']
    },
    {
      fields: ['createdAt']
    },
    {
      fields: ['lastAnswerAt']
    },
    {
      fields: ['acceptedAnswerId']
    },
    {
      name: 'questions_search_idx',
      fields: ['title', 'content'],
      type: 'FULLTEXT'
    }
  ]
})

// 实例方法
Question.prototype.incrementViewCount = async function() {
  return this.increment('viewCount')
}

Question.prototype.incrementAnswerCount = async function() {
  return this.increment('answerCount')
}

Question.prototype.decrementAnswerCount = async function() {
  return this.decrement('answerCount')
}

Question.prototype.incrementFavoriteCount = async function() {
  return this.increment('favoriteCount')
}

Question.prototype.decrementFavoriteCount = async function() {
  return this.decrement('favoriteCount')
}

Question.prototype.incrementLikeCount = async function() {
  return this.increment('likeCount')
}

Question.prototype.decrementLikeCount = async function() {
  return this.decrement('likeCount')
}

Question.prototype.incrementDislikeCount = async function() {
  return this.increment('dislikeCount')
}

Question.prototype.decrementDislikeCount = async function() {
  return this.decrement('dislikeCount')
}

Question.prototype.updateScore = async function() {
  // 计算问题评分：点赞数 - 点踩数 + 回答数 * 0.5 + 收藏数 * 0.3
  const score = this.likeCount - this.dislikeCount + this.answerCount * 0.5 + this.favoriteCount * 0.3
  return this.update({ score })
}

Question.prototype.setAcceptedAnswer = async function(answerId) {
  return this.update({ acceptedAnswerId: answerId })
}

Question.prototype.removeAcceptedAnswer = async function() {
  return this.update({ acceptedAnswerId: null })
}

Question.prototype.close = async function(reason) {
  return this.update({
    status: 'closed',
    closedAt: new Date(),
    closedReason: reason
  })
}

Question.prototype.reopen = async function() {
  return this.update({
    status: 'published',
    closedAt: null,
    closedReason: null
  })
}

Question.prototype.canBeViewedBy = function(user) {
  // 检查是否需要会员权限
  if (this.requireMembership > 0) {
    if (!user) return false
    if (user.memberLevel < this.requireMembership) return false
  }
  
  // 检查问题状态
  if (this.status === 'deleted') return false
  if (this.status === 'draft' && (!user || user.id !== this.userId)) return false
  if (this.status === 'pending' && (!user || (user.role !== 'admin' && user.id !== this.userId))) return false
  
  return true
}

Question.prototype.canBeEditedBy = function(user) {
  if (!user) return false
  if (user.role === 'admin') return true
  if (user.id === this.userId && this.status !== 'deleted') return true
  return false
}

Question.prototype.canBeDeletedBy = function(user) {
  if (!user) return false
  if (user.role === 'admin') return true
  if (user.id === this.userId && this.answerCount === 0) return true
  return false
}

Question.prototype.generateBrief = function() {
  if (this.brief) return this.brief
  
  // 从内容中提取前200个字符作为简介
  const plainText = this.content.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim()
  return plainText.length > 200 ? plainText.substring(0, 200) + '...' : plainText
}

Question.prototype.updateSearchVector = async function() {
  // 更新搜索向量
  const searchText = `${this.title} ${this.content} ${this.tags.join(' ')}`
  return this.update({ searchVector: searchText })
}

// 类方法
Question.findPublished = function(options = {}) {
  return this.findAll({
    where: {
      status: 'published',
      ...options.where
    },
    ...options
  })
}

Question.findByUser = function(userId, options = {}) {
  return this.findAll({
    where: {
      userId,
      status: { [sequelize.Sequelize.Op.ne]: 'deleted' },
      ...options.where
    },
    ...options
  })
}

Question.findByCategory = function(categoryId, options = {}) {
  return this.findAll({
    where: {
      categoryId,
      status: 'published',
      ...options.where
    },
    ...options
  })
}

Question.findByTag = function(tag, options = {}) {
  return this.findAll({
    where: {
      tags: {
        [sequelize.Sequelize.Op.contains]: [tag]
      },
      status: 'published',
      ...options.where
    },
    ...options
  })
}

Question.search = function(keyword, options = {}) {
  return this.findAll({
    where: {
      [sequelize.Sequelize.Op.or]: [
        {
          title: {
            [sequelize.Sequelize.Op.like]: `%${keyword}%`
          }
        },
        {
          content: {
            [sequelize.Sequelize.Op.like]: `%${keyword}%`
          }
        },
        {
          searchVector: {
            [sequelize.Sequelize.Op.like]: `%${keyword}%`
          }
        }
      ],
      status: 'published',
      ...options.where
    },
    ...options
  })
}

module.exports = Question
