const { Sequelize } = require('sequelize')
const config = require('./index')
const logger = require('../utils/logger')

// 创建Sequelize实例
const sequelize = new Sequelize(
  config.database.database,
  config.database.username,
  config.database.password,
  {
    host: config.database.host,
    port: config.database.port,
    dialect: config.database.dialect,
    timezone: config.database.timezone,
    logging: config.database.logging,
    pool: config.database.pool,
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: true,
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci'
    },
    dialectOptions: {
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci',
      supportBigNumbers: true,
      bigNumberStrings: true
    }
  }
)

// 连接数据库
const connectDatabase = async () => {
  try {
    await sequelize.authenticate()
    logger.info('Database connection established successfully')
    
    // 在开发环境下同步数据库
    if (config.NODE_ENV === 'development') {
      await sequelize.sync({ alter: true })
      logger.info('Database synchronized successfully')
    }
    
    return sequelize
  } catch (error) {
    logger.error('Unable to connect to the database:', error)
    throw error
  }
}

// 关闭数据库连接
const closeDatabase = async () => {
  try {
    await sequelize.close()
    logger.info('Database connection closed')
  } catch (error) {
    logger.error('Error closing database connection:', error)
    throw error
  }
}

module.exports = {
  sequelize,
  connectDatabase,
  closeDatabase
}
