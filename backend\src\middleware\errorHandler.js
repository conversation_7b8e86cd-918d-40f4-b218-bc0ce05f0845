const logger = require('../utils/logger')
const { ApiError } = require('../utils/errors')

// 错误处理中间件
const errorHandler = (error, req, res, next) => {
  let statusCode = 500
  let message = '服务器内部错误'
  let code = 'INTERNAL_SERVER_ERROR'
  let details = null

  // 记录错误日志
  logger.logError(error, req)

  // 处理不同类型的错误
  if (error instanceof ApiError) {
    // 自定义API错误
    statusCode = error.statusCode
    message = error.message
    code = error.code || 'API_ERROR'
  } else if (error.name === 'ValidationError') {
    // 数据验证错误
    statusCode = 400
    message = '数据验证失败'
    code = 'VALIDATION_ERROR'
    details = error.errors || error.details
  } else if (error.name === 'SequelizeValidationError') {
    // Sequelize验证错误
    statusCode = 400
    message = '数据验证失败'
    code = 'VALIDATION_ERROR'
    details = error.errors.map(err => ({
      field: err.path,
      message: err.message,
      value: err.value
    }))
  } else if (error.name === 'SequelizeUniqueConstraintError') {
    // 唯一约束错误
    statusCode = 409
    message = '数据已存在'
    code = 'DUPLICATE_ERROR'
    details = error.errors.map(err => ({
      field: err.path,
      message: `${err.path} 已存在`,
      value: err.value
    }))
  } else if (error.name === 'SequelizeForeignKeyConstraintError') {
    // 外键约束错误
    statusCode = 400
    message = '关联数据不存在'
    code = 'FOREIGN_KEY_ERROR'
  } else if (error.name === 'SequelizeDatabaseError') {
    // 数据库错误
    statusCode = 500
    message = '数据库操作失败'
    code = 'DATABASE_ERROR'
  } else if (error.name === 'JsonWebTokenError') {
    // JWT错误
    statusCode = 401
    message = '无效的访问令牌'
    code = 'INVALID_TOKEN'
  } else if (error.name === 'TokenExpiredError') {
    // JWT过期错误
    statusCode = 401
    message = '访问令牌已过期'
    code = 'TOKEN_EXPIRED'
  } else if (error.name === 'MulterError') {
    // 文件上传错误
    statusCode = 400
    code = 'UPLOAD_ERROR'
    
    switch (error.code) {
      case 'LIMIT_FILE_SIZE':
        message = '文件大小超出限制'
        break
      case 'LIMIT_FILE_COUNT':
        message = '文件数量超出限制'
        break
      case 'LIMIT_UNEXPECTED_FILE':
        message = '意外的文件字段'
        break
      default:
        message = '文件上传失败'
    }
  } else if (error.code === 'ECONNREFUSED') {
    // 连接被拒绝错误（如数据库连接失败）
    statusCode = 503
    message = '服务暂时不可用'
    code = 'SERVICE_UNAVAILABLE'
  } else if (error.code === 'ENOTFOUND') {
    // DNS解析错误
    statusCode = 502
    message = '外部服务不可达'
    code = 'EXTERNAL_SERVICE_ERROR'
  } else if (error.type === 'entity.parse.failed') {
    // JSON解析错误
    statusCode = 400
    message = '请求数据格式错误'
    code = 'INVALID_JSON'
  } else if (error.type === 'entity.too.large') {
    // 请求体过大
    statusCode = 413
    message = '请求数据过大'
    code = 'PAYLOAD_TOO_LARGE'
  }

  // 构建错误响应
  const errorResponse = {
    code: statusCode,
    message,
    error: {
      type: code,
      timestamp: new Date().toISOString(),
      path: req.originalUrl,
      method: req.method
    }
  }

  // 添加详细信息（仅在开发环境或验证错误时）
  if (details || (process.env.NODE_ENV === 'development' && statusCode >= 500)) {
    errorResponse.error.details = details || {
      stack: error.stack,
      name: error.name
    }
  }

  // 添加请求ID（如果存在）
  if (req.requestId) {
    errorResponse.error.requestId = req.requestId
  }

  // 发送错误响应
  res.status(statusCode).json(errorResponse)
}

// 404错误处理中间件
const notFoundHandler = (req, res) => {
  const error = {
    code: 404,
    message: '请求的资源不存在',
    error: {
      type: 'NOT_FOUND',
      timestamp: new Date().toISOString(),
      path: req.originalUrl,
      method: req.method
    }
  }

  logger.warn('404 Not Found', {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id
  })

  res.status(404).json(error)
}

// 异步错误包装器
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next)
  }
}

// 错误边界装饰器
const withErrorBoundary = (controller) => {
  return asyncHandler(async (req, res, next) => {
    try {
      await controller(req, res, next)
    } catch (error) {
      // 确保错误被正确传递到错误处理中间件
      next(error)
    }
  })
}

module.exports = {
  errorHandler,
  notFoundHandler,
  asyncHandler,
  withErrorBoundary
}
