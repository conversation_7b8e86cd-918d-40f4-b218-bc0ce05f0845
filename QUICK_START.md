# Materials Studio答疑平台 - 快速开始指南

## 🚀 快速启动

### 方式一：使用启动脚本（推荐）

#### Windows用户
```bash
# 安装依赖
start.bat install

# 启动开发环境
start.bat dev
```

#### Linux/Mac用户
```bash
# 给脚本执行权限
chmod +x start.sh

# 安装依赖
./start.sh install

# 启动开发环境
./start.sh dev
```

### 方式二：手动启动

#### 1. 安装依赖
```bash
# 后端依赖
cd backend
npm install

# 前端依赖
cd ../frontend
npm install
```

#### 2. 配置环境变量
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件（可选，使用默认配置即可快速体验）
# 编辑 .env 文件
```

#### 3. 启动服务

**启动数据库和Redis（使用Docker）：**
```bash
docker-compose up -d mysql redis
```

**启动后端服务：**
```bash
cd backend
npm run dev
```

**启动前端服务：**
```bash
cd frontend
npm run serve
```

## 📱 访问应用

启动成功后，您可以通过以下地址访问：

- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:3001
- **API文档**: http://localhost:3001/api-docs

## 👤 默认账号

系统已预置管理员账号：

- **用户名**: admin
- **密码**: Admin@123

## 🛠️ 开发环境要求

### 必需软件
- **Node.js** >= 16.0.0
- **npm** >= 8.0.0
- **Docker** >= 20.0.0
- **Docker Compose** >= 2.0.0

### 可选软件
- **Git** - 版本控制
- **VS Code** - 推荐的代码编辑器
- **Postman** - API测试工具

## 📁 项目结构

```
materials-studio-platform/
├── frontend/                 # Vue.js前端应用
│   ├── src/
│   │   ├── components/      # 公共组件
│   │   ├── views/          # 页面组件
│   │   ├── stores/         # Pinia状态管理
│   │   ├── router/         # 路由配置
│   │   └── api/            # API接口
│   └── package.json
├── backend/                  # Node.js后端应用
│   ├── src/
│   │   ├── controllers/    # 控制器
│   │   ├── models/         # 数据模型
│   │   ├── routes/         # 路由定义
│   │   ├── middleware/     # 中间件
│   │   └── services/       # 业务逻辑
│   └── package.json
├── database/                 # 数据库相关
│   └── init.sql            # 初始化脚本
├── docker-compose.yml        # Docker编排文件
├── .env.example             # 环境变量示例
├── start.sh                 # Linux/Mac启动脚本
├── start.bat                # Windows启动脚本
└── README.md
```

## 🔧 常用命令

### 开发命令
```bash
# 启动开发环境
npm run dev          # 后端开发模式
npm run serve        # 前端开发模式

# 代码检查和格式化
npm run lint         # 代码检查
npm run format       # 代码格式化

# 测试
npm test             # 运行测试
npm run test:watch   # 监听模式测试
```

### Docker命令
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down

# 重建服务
docker-compose up -d --build
```

## 🐛 常见问题

### 1. 端口被占用
如果遇到端口被占用的问题：

**Windows:**
```bash
# 查看端口占用
netstat -ano | findstr :3000
netstat -ano | findstr :3001

# 结束进程
taskkill /PID <进程ID> /F
```

**Linux/Mac:**
```bash
# 查看端口占用
lsof -i :3000
lsof -i :3001

# 结束进程
kill -9 <进程ID>
```

### 2. 数据库连接失败
确保Docker服务正在运行：
```bash
# 检查Docker状态
docker ps

# 重启数据库服务
docker-compose restart mysql
```

### 3. 前端编译错误
清除缓存并重新安装依赖：
```bash
cd frontend
rm -rf node_modules package-lock.json
npm install
```

### 4. 后端启动失败
检查环境变量配置：
```bash
# 确保.env文件存在
cp .env.example .env

# 检查数据库配置
cat .env | grep DB_
```

## 📚 功能特性

### 已实现功能
- ✅ 用户注册/登录系统
- ✅ 问答系统基础框架
- ✅ 响应式UI设计
- ✅ JWT身份认证
- ✅ 数据库模型设计
- ✅ Docker容器化部署

### 开发中功能
- 🚧 问题发布与回答
- 🚧 会员系统
- 🚧 资源下载
- 🚧 支付集成
- 🚧 管理后台
- 🚧 搜索功能

### 计划功能
- 📋 实时通知
- 📋 专家咨询
- 📋 积分系统
- 📋 社交功能
- 📋 移动端适配

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📞 技术支持

如果您在使用过程中遇到问题，可以通过以下方式获取帮助：

- 📧 邮箱: <EMAIL>
- 💬 QQ群: 123456789
- 📱 微信群: 扫描二维码加入

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

---

**Materials Studio答疑平台** - 让分子模拟学习更简单！
