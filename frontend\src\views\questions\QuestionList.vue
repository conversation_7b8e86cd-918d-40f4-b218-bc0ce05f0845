<template>
  <div class="question-list-page">
    <AppHeader />
    
    <main class="main-content">
      <div class="container">
        <div class="page-layout">
          <!-- 侧边栏 -->
          <aside class="sidebar">
            <!-- 分类筛选 -->
            <div class="filter-section">
              <h3 class="filter-title">分类筛选</h3>
              <div class="category-list">
                <div 
                  class="category-item"
                  :class="{ active: !selectedCategory }"
                  @click="filterByCategory(null)"
                >
                  <span>全部分类</span>
                  <span class="count">{{ totalCount }}</span>
                </div>
                <div 
                  v-for="category in categories"
                  :key="category.id"
                  class="category-item"
                  :class="{ active: selectedCategory === category.id }"
                  @click="filterByCategory(category.id)"
                >
                  <span>{{ category.name }}</span>
                  <span class="count">{{ category.questionCount }}</span>
                </div>
              </div>
            </div>

            <!-- 热门标签 -->
            <div class="filter-section">
              <h3 class="filter-title">热门标签</h3>
              <div class="tag-list">
                <el-tag
                  v-for="tag in popularTags"
                  :key="tag.name"
                  :type="selectedTag === tag.name ? 'primary' : 'info'"
                  effect="light"
                  size="small"
                  class="tag-item"
                  @click="filterByTag(tag.name)"
                >
                  {{ tag.name }}
                </el-tag>
              </div>
            </div>
          </aside>

          <!-- 主内容区 -->
          <div class="content-area">
            <!-- 页面头部 -->
            <div class="page-header">
              <div class="header-left">
                <h1 class="page-title">问题广场</h1>
                <p class="page-subtitle">探索Materials Studio相关问题，分享您的知识</p>
              </div>
              <div class="header-right">
                <el-button 
                  type="primary" 
                  @click="$router.push('/ask')"
                  v-if="userStore.isAuthenticated"
                >
                  <el-icon><Edit /></el-icon>
                  我要提问
                </el-button>
              </div>
            </div>

            <!-- 搜索和筛选 -->
            <div class="search-filter-bar">
              <div class="search-box">
                <el-input
                  v-model="searchKeyword"
                  placeholder="搜索问题..."
                  @keyup.enter="handleSearch"
                  clearable
                  size="large"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                  <template #append>
                    <el-button @click="handleSearch">搜索</el-button>
                  </template>
                </el-input>
              </div>
              
              <div class="filter-controls">
                <el-select v-model="sortBy" @change="handleSort" placeholder="排序方式">
                  <el-option label="最新发布" value="created_at" />
                  <el-option label="最多回答" value="answer_count" />
                  <el-option label="最多浏览" value="view_count" />
                  <el-option label="最高评分" value="score" />
                </el-select>
                
                <el-select v-model="difficultyFilter" @change="handleFilter" placeholder="难度筛选">
                  <el-option label="全部难度" value="" />
                  <el-option label="初学者" value="beginner" />
                  <el-option label="中级" value="intermediate" />
                  <el-option label="高级" value="advanced" />
                  <el-option label="专家" value="expert" />
                </el-select>
              </div>
            </div>

            <!-- 问题列表 -->
            <div class="question-list" v-loading="questionsStore.isLoading">
              <div v-if="questionsStore.questions.length === 0" class="empty-state">
                <el-empty description="暂无问题" />
              </div>
              
              <div v-else class="question-items">
                <QuestionCard
                  v-for="question in questionsStore.questions"
                  :key="question.id"
                  :question="question"
                  @click="goToQuestion(question.id)"
                />
              </div>

              <!-- 加载更多 -->
              <div class="load-more" v-if="questionsStore.hasMore">
                <el-button 
                  @click="loadMore" 
                  :loading="isLoadingMore"
                  size="large"
                >
                  加载更多
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <AppFooter />
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useQuestionsStore } from '@/stores/questions'
import AppHeader from '@/components/layout/AppHeader.vue'
import AppFooter from '@/components/layout/AppFooter.vue'
import QuestionCard from '@/components/questions/QuestionCard.vue'
import { Search, Edit } from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const questionsStore = useQuestionsStore()

// 响应式数据
const searchKeyword = ref(route.query.keyword || '')
const selectedCategory = ref(null)
const selectedTag = ref(null)
const sortBy = ref('created_at')
const difficultyFilter = ref('')
const isLoadingMore = ref(false)
const totalCount = ref(0)

// 分类数据
const categories = ref([
  { id: 1, name: 'Materials Studio基础', questionCount: 156 },
  { id: 2, name: '分子动力学', questionCount: 89 },
  { id: 3, name: 'DFT计算', questionCount: 134 },
  { id: 4, name: '晶体结构', questionCount: 67 },
  { id: 5, name: '表面与界面', questionCount: 45 },
  { id: 6, name: '高分子材料', questionCount: 78 },
  { id: 7, name: '纳米材料', questionCount: 92 },
  { id: 8, name: '软件技巧', questionCount: 123 }
])

// 热门标签
const popularTags = ref([
  { name: 'CASTEP', count: 234 },
  { name: 'DMol3', count: 189 },
  { name: 'Forcite', count: 156 },
  { name: 'Adsorption', count: 134 },
  { name: 'Crystal', count: 123 },
  { name: 'Polymer', count: 98 },
  { name: 'Surface', count: 87 },
  { name: 'Optimization', count: 76 }
])

// 搜索处理
const handleSearch = async () => {
  await questionsStore.searchQuestions(searchKeyword.value)
  updateUrl()
}

// 分类筛选
const filterByCategory = async (categoryId) => {
  selectedCategory.value = categoryId
  selectedTag.value = null
  await questionsStore.filterByCategory(categoryId)
  updateUrl()
}

// 标签筛选
const filterByTag = async (tagName) => {
  selectedTag.value = selectedTag.value === tagName ? null : tagName
  selectedCategory.value = null
  if (selectedTag.value) {
    await questionsStore.filterByTag(tagName)
  } else {
    await questionsStore.fetchQuestions()
  }
  updateUrl()
}

// 排序处理
const handleSort = async () => {
  await questionsStore.sortQuestions(sortBy.value)
  updateUrl()
}

// 筛选处理
const handleFilter = async () => {
  const filters = {}
  if (difficultyFilter.value) {
    filters.difficulty = difficultyFilter.value
  }
  await questionsStore.fetchQuestions(filters)
  updateUrl()
}

// 加载更多
const loadMore = async () => {
  try {
    isLoadingMore.value = true
    await questionsStore.loadMore()
  } finally {
    isLoadingMore.value = false
  }
}

// 跳转到问题详情
const goToQuestion = (questionId) => {
  router.push(`/questions/${questionId}`)
}

// 更新URL参数
const updateUrl = () => {
  const query = {}
  if (searchKeyword.value) query.keyword = searchKeyword.value
  if (selectedCategory.value) query.category = selectedCategory.value
  if (selectedTag.value) query.tag = selectedTag.value
  if (sortBy.value !== 'created_at') query.sort = sortBy.value
  if (difficultyFilter.value) query.difficulty = difficultyFilter.value
  
  router.replace({ query })
}

// 监听路由变化
watch(() => route.query, (newQuery) => {
  searchKeyword.value = newQuery.keyword || ''
  selectedCategory.value = newQuery.category ? parseInt(newQuery.category) : null
  selectedTag.value = newQuery.tag || null
  sortBy.value = newQuery.sort || 'created_at'
  difficultyFilter.value = newQuery.difficulty || ''
}, { immediate: true })

onMounted(async () => {
  // 获取分类列表
  await questionsStore.fetchCategories()
  
  // 获取标签列表
  await questionsStore.fetchTags()
  
  // 根据URL参数加载问题
  const filters = {}
  if (route.query.keyword) {
    await questionsStore.searchQuestions(route.query.keyword)
  } else if (route.query.category) {
    await questionsStore.filterByCategory(parseInt(route.query.category))
  } else if (route.query.tag) {
    await questionsStore.filterByTag(route.query.tag)
  } else {
    if (route.query.difficulty) filters.difficulty = route.query.difficulty
    if (route.query.sort) {
      await questionsStore.sortQuestions(route.query.sort)
    } else {
      await questionsStore.fetchQuestions(filters)
    }
  }
  
  // 计算总数
  totalCount.value = categories.value.reduce((sum, cat) => sum + cat.questionCount, 0)
})
</script>

<style lang="scss" scoped>
.question-list-page {
  min-height: 100vh;
  background: var(--bg-secondary);
}

.main-content {
  padding-top: 80px;
  min-height: calc(100vh - 80px);
}

.page-layout {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 32px;
  padding: 32px 0;
  
  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
    gap: 24px;
  }
}

// 侧边栏
.sidebar {
  @media (max-width: 1024px) {
    display: none;
  }
}

.filter-section {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: var(--shadow-sm);
  
  .filter-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 16px;
  }
}

.category-list {
  .category-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    margin-bottom: 4px;
    
    &:hover {
      background: var(--bg-secondary);
    }
    
    &.active {
      background: var(--primary-color);
      color: white;
    }
    
    .count {
      font-size: 0.875rem;
      color: var(--text-light);
      
      .active & {
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  
  .tag-item {
    cursor: pointer;
    transition: all var(--transition-fast);
    
    &:hover {
      transform: translateY(-1px);
    }
  }
}

// 主内容区
.content-area {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 32px;
  border-bottom: 1px solid var(--border-light);
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .page-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 8px;
  }
  
  .page-subtitle {
    color: var(--text-secondary);
    margin: 0;
  }
}

.search-filter-bar {
  display: flex;
  gap: 16px;
  padding: 24px 32px;
  border-bottom: 1px solid var(--border-light);
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
  }
  
  .search-box {
    flex: 1;
  }
  
  .filter-controls {
    display: flex;
    gap: 12px;
    
    @media (max-width: 768px) {
      flex-wrap: wrap;
    }
    
    .el-select {
      width: 140px;
      
      @media (max-width: 768px) {
        width: 100%;
      }
    }
  }
}

.question-list {
  padding: 24px 32px;
  
  @media (max-width: 768px) {
    padding: 16px;
  }
}

.empty-state {
  padding: 60px 0;
  text-align: center;
}

.question-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.load-more {
  text-align: center;
  margin-top: 32px;
  padding-top: 32px;
  border-top: 1px solid var(--border-light);
}
</style>
