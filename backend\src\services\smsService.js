const config = require('../config')
const logger = require('../utils/logger')
const { ExternalServiceError } = require('../utils/errors')

class SmsService {
  constructor() {
    this.client = null
    this.initializeClient()
  }

  // 初始化短信客户端
  initializeClient() {
    try {
      // 这里可以集成阿里云短信、腾讯云短信等服务
      // 示例使用阿里云短信服务
      if (config.sms.accessKeyId && config.sms.accessKeySecret) {
        // const Core = require('@alicloud/pop-core')
        // this.client = new Core({
        //   accessKeyId: config.sms.accessKeyId,
        //   accessKeySecret: config.sms.accessKeySecret,
        //   endpoint: 'https://dysmsapi.aliyuncs.com',
        //   apiVersion: '2017-05-25'
        // })
        logger.info('SMS service initialized successfully')
      } else {
        logger.warn('SMS service not configured, using mock mode')
      }
    } catch (error) {
      logger.error('Failed to initialize SMS service:', error)
      throw new ExternalServiceError('短信服务初始化失败', 'sms')
    }
  }

  // 发送短信的通用方法
  async sendSms(phone, templateCode, templateParams = {}) {
    try {
      // 开发环境或未配置时使用模拟模式
      if (process.env.NODE_ENV === 'development' || !this.client) {
        return this.mockSendSms(phone, templateCode, templateParams)
      }

      // 生产环境使用真实短信服务
      const params = {
        PhoneNumbers: phone,
        SignName: config.sms.signName,
        TemplateCode: templateCode,
        TemplateParam: JSON.stringify(templateParams)
      }

      const requestOption = {
        method: 'POST'
      }

      const result = await this.client.request('SendSms', params, requestOption)
      
      if (result.Code === 'OK') {
        logger.info('SMS sent successfully', {
          phone: this.maskPhone(phone),
          templateCode,
          bizId: result.BizId
        })

        return {
          success: true,
          bizId: result.BizId,
          message: '短信发送成功'
        }
      } else {
        logger.error('SMS sending failed', {
          phone: this.maskPhone(phone),
          code: result.Code,
          message: result.Message
        })

        throw new ExternalServiceError(`短信发送失败: ${result.Message}`, 'sms')
      }
    } catch (error) {
      logger.error('SMS service error:', error)
      throw new ExternalServiceError('短信发送失败', 'sms')
    }
  }

  // 发送验证码短信
  async sendVerificationCode(phone, code) {
    const templateParams = {
      code: code
    }

    return await this.sendSms(phone, config.sms.templateCode, templateParams)
  }

  // 发送登录验证码
  async sendLoginCode(phone, code) {
    const templateParams = {
      code: code
    }

    // 可以使用不同的模板
    return await this.sendSms(phone, config.sms.loginTemplateCode || config.sms.templateCode, templateParams)
  }

  // 发送密码重置验证码
  async sendPasswordResetCode(phone, code) {
    const templateParams = {
      code: code
    }

    return await this.sendSms(phone, config.sms.resetTemplateCode || config.sms.templateCode, templateParams)
  }

  // 发送会员到期提醒
  async sendMembershipExpiryNotice(phone, username, days) {
    const templateParams = {
      name: username,
      days: days
    }

    return await this.sendSms(phone, config.sms.membershipExpiryTemplateCode, templateParams)
  }

  // 发送系统通知
  async sendSystemNotification(phone, message) {
    const templateParams = {
      message: message
    }

    return await this.sendSms(phone, config.sms.systemNotificationTemplateCode, templateParams)
  }

  // 模拟发送短信（开发环境使用）
  async mockSendSms(phone, templateCode, templateParams) {
    logger.info('Mock SMS sent', {
      phone: this.maskPhone(phone),
      templateCode,
      templateParams
    })

    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    return {
      success: true,
      bizId: `mock_${Date.now()}`,
      message: '短信发送成功（模拟）'
    }
  }

  // 验证手机号格式
  validatePhoneNumber(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  }

  // 掩码手机号（用于日志记录）
  maskPhone(phone) {
    if (!phone || phone.length < 7) return phone
    return phone.substring(0, 3) + '****' + phone.substring(phone.length - 4)
  }

  // 检查短信发送频率限制
  async checkSendingLimit(phone) {
    // 这里可以实现更复杂的频率限制逻辑
    // 例如：每天最多发送10条，每小时最多3条等
    
    const dailyKey = `sms_daily:${phone}:${new Date().toDateString()}`
    const hourlyKey = `sms_hourly:${phone}:${new Date().getHours()}`
    
    // 使用Redis检查发送次数
    const { redisUtils } = require('../config/redis')
    
    const dailyCount = await redisUtils.get(dailyKey) || 0
    const hourlyCount = await redisUtils.get(hourlyKey) || 0
    
    if (dailyCount >= 10) {
      throw new ExternalServiceError('今日短信发送次数已达上限', 'sms')
    }
    
    if (hourlyCount >= 3) {
      throw new ExternalServiceError('本小时短信发送次数已达上限', 'sms')
    }
    
    return true
  }

  // 记录短信发送次数
  async recordSendingCount(phone) {
    const dailyKey = `sms_daily:${phone}:${new Date().toDateString()}`
    const hourlyKey = `sms_hourly:${phone}:${new Date().getHours()}`
    
    const { redisUtils } = require('../config/redis')
    
    // 增加计数
    await redisUtils.incr(dailyKey)
    await redisUtils.incr(hourlyKey)
    
    // 设置过期时间
    await redisUtils.expire(dailyKey, 24 * 60 * 60) // 24小时
    await redisUtils.expire(hourlyKey, 60 * 60) // 1小时
  }

  // 批量发送短信
  async sendBatchSms(phones, templateCode, templateParams = {}) {
    const results = []
    
    for (const phone of phones) {
      try {
        const result = await this.sendSms(phone, templateCode, templateParams)
        results.push({
          phone: this.maskPhone(phone),
          success: true,
          result
        })
      } catch (error) {
        results.push({
          phone: this.maskPhone(phone),
          success: false,
          error: error.message
        })
      }
      
      // 批量发送时添加延迟，避免触发频率限制
      await new Promise(resolve => setTimeout(resolve, 100))
    }
    
    return results
  }

  // 获取短信发送状态
  async getSmsStatus(bizId) {
    try {
      if (!this.client) {
        return { status: 'unknown', message: '短信服务未配置' }
      }

      const params = {
        BizId: bizId,
        SendDate: new Date().toISOString().split('T')[0].replace(/-/g, '')
      }

      const requestOption = {
        method: 'POST'
      }

      const result = await this.client.request('QuerySendDetails', params, requestOption)
      
      if (result.Code === 'OK' && result.SmsSendDetailDTOs.SmsSendDetailDTO.length > 0) {
        const detail = result.SmsSendDetailDTOs.SmsSendDetailDTO[0]
        return {
          status: detail.SendStatus,
          receiveDate: detail.ReceiveDate,
          errorCode: detail.ErrCode,
          message: detail.Content
        }
      } else {
        return { status: 'unknown', message: '查询失败' }
      }
    } catch (error) {
      logger.error('Failed to query SMS status:', error)
      return { status: 'error', message: '查询异常' }
    }
  }

  // 获取短信模板列表
  getTemplateList() {
    return {
      verification: {
        code: config.sms.templateCode,
        description: '验证码短信'
      },
      login: {
        code: config.sms.loginTemplateCode,
        description: '登录验证码'
      },
      reset: {
        code: config.sms.resetTemplateCode,
        description: '密码重置验证码'
      },
      membershipExpiry: {
        code: config.sms.membershipExpiryTemplateCode,
        description: '会员到期提醒'
      },
      systemNotification: {
        code: config.sms.systemNotificationTemplateCode,
        description: '系统通知'
      }
    }
  }
}

module.exports = new SmsService()
