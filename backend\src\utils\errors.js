// 自定义错误类
class ApiError extends Error {
  constructor(statusCode, message, isOperational = true, stack = '') {
    super(message)
    this.statusCode = statusCode
    this.isOperational = isOperational
    this.timestamp = new Date().toISOString()
    
    if (stack) {
      this.stack = stack
    } else {
      Error.captureStackTrace(this, this.constructor)
    }
  }
}

class ValidationError extends ApiError {
  constructor(message, errors = []) {
    super(400, message)
    this.name = 'ValidationError'
    this.errors = errors
  }
}

class AuthenticationError extends ApiError {
  constructor(message = '身份验证失败') {
    super(401, message)
    this.name = 'AuthenticationError'
  }
}

class AuthorizationError extends ApiError {
  constructor(message = '权限不足') {
    super(403, message)
    this.name = 'AuthorizationError'
  }
}

class NotFoundError extends ApiError {
  constructor(message = '资源未找到') {
    super(404, message)
    this.name = 'NotFoundError'
  }
}

class ConflictError extends ApiError {
  constructor(message = '资源冲突') {
    super(409, message)
    this.name = 'ConflictError'
  }
}

class RateLimitError extends ApiError {
  constructor(message = '请求过于频繁') {
    super(429, message)
    this.name = 'RateLimitError'
  }
}

class InternalServerError extends ApiError {
  constructor(message = '服务器内部错误') {
    super(500, message)
    this.name = 'InternalServerError'
  }
}

class DatabaseError extends ApiError {
  constructor(message = '数据库操作失败') {
    super(500, message)
    this.name = 'DatabaseError'
  }
}

class ExternalServiceError extends ApiError {
  constructor(message = '外部服务错误', service = '') {
    super(502, message)
    this.name = 'ExternalServiceError'
    this.service = service
  }
}

// 错误代码映射
const ERROR_CODES = {
  // 通用错误
  INVALID_REQUEST: 'INVALID_REQUEST',
  INVALID_PARAMETERS: 'INVALID_PARAMETERS',
  MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
  
  // 认证错误
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  TOKEN_INVALID: 'TOKEN_INVALID',
  ACCOUNT_LOCKED: 'ACCOUNT_LOCKED',
  ACCOUNT_DISABLED: 'ACCOUNT_DISABLED',
  
  // 授权错误
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  MEMBERSHIP_REQUIRED: 'MEMBERSHIP_REQUIRED',
  ADMIN_REQUIRED: 'ADMIN_REQUIRED',
  
  // 资源错误
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  RESOURCE_ALREADY_EXISTS: 'RESOURCE_ALREADY_EXISTS',
  RESOURCE_CONFLICT: 'RESOURCE_CONFLICT',
  
  // 用户错误
  USER_NOT_FOUND: 'USER_NOT_FOUND',
  USER_ALREADY_EXISTS: 'USER_ALREADY_EXISTS',
  EMAIL_ALREADY_EXISTS: 'EMAIL_ALREADY_EXISTS',
  USERNAME_ALREADY_EXISTS: 'USERNAME_ALREADY_EXISTS',
  PHONE_ALREADY_EXISTS: 'PHONE_ALREADY_EXISTS',
  
  // 验证错误
  VERIFICATION_CODE_INVALID: 'VERIFICATION_CODE_INVALID',
  VERIFICATION_CODE_EXPIRED: 'VERIFICATION_CODE_EXPIRED',
  EMAIL_NOT_VERIFIED: 'EMAIL_NOT_VERIFIED',
  PHONE_NOT_VERIFIED: 'PHONE_NOT_VERIFIED',
  
  // 业务错误
  QUESTION_NOT_FOUND: 'QUESTION_NOT_FOUND',
  ANSWER_NOT_FOUND: 'ANSWER_NOT_FOUND',
  COMMENT_NOT_FOUND: 'COMMENT_NOT_FOUND',
  CATEGORY_NOT_FOUND: 'CATEGORY_NOT_FOUND',
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  
  // 文件错误
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
  FILE_TYPE_NOT_ALLOWED: 'FILE_TYPE_NOT_ALLOWED',
  UPLOAD_FAILED: 'UPLOAD_FAILED',
  
  // 支付错误
  PAYMENT_FAILED: 'PAYMENT_FAILED',
  PAYMENT_CANCELLED: 'PAYMENT_CANCELLED',
  PAYMENT_EXPIRED: 'PAYMENT_EXPIRED',
  INSUFFICIENT_BALANCE: 'INSUFFICIENT_BALANCE',
  
  // 系统错误
  DATABASE_ERROR: 'DATABASE_ERROR',
  CACHE_ERROR: 'CACHE_ERROR',
  EMAIL_SERVICE_ERROR: 'EMAIL_SERVICE_ERROR',
  SMS_SERVICE_ERROR: 'SMS_SERVICE_ERROR',
  EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR'
}

// 错误消息映射
const ERROR_MESSAGES = {
  [ERROR_CODES.INVALID_REQUEST]: '请求无效',
  [ERROR_CODES.INVALID_PARAMETERS]: '参数无效',
  [ERROR_CODES.MISSING_REQUIRED_FIELD]: '缺少必填字段',
  
  [ERROR_CODES.INVALID_CREDENTIALS]: '用户名或密码错误',
  [ERROR_CODES.TOKEN_EXPIRED]: '令牌已过期',
  [ERROR_CODES.TOKEN_INVALID]: '令牌无效',
  [ERROR_CODES.ACCOUNT_LOCKED]: '账户已被锁定',
  [ERROR_CODES.ACCOUNT_DISABLED]: '账户已被禁用',
  
  [ERROR_CODES.INSUFFICIENT_PERMISSIONS]: '权限不足',
  [ERROR_CODES.MEMBERSHIP_REQUIRED]: '需要会员权限',
  [ERROR_CODES.ADMIN_REQUIRED]: '需要管理员权限',
  
  [ERROR_CODES.RESOURCE_NOT_FOUND]: '资源未找到',
  [ERROR_CODES.RESOURCE_ALREADY_EXISTS]: '资源已存在',
  [ERROR_CODES.RESOURCE_CONFLICT]: '资源冲突',
  
  [ERROR_CODES.USER_NOT_FOUND]: '用户不存在',
  [ERROR_CODES.USER_ALREADY_EXISTS]: '用户已存在',
  [ERROR_CODES.EMAIL_ALREADY_EXISTS]: '邮箱已被注册',
  [ERROR_CODES.USERNAME_ALREADY_EXISTS]: '用户名已存在',
  [ERROR_CODES.PHONE_ALREADY_EXISTS]: '手机号已被注册',
  
  [ERROR_CODES.VERIFICATION_CODE_INVALID]: '验证码无效',
  [ERROR_CODES.VERIFICATION_CODE_EXPIRED]: '验证码已过期',
  [ERROR_CODES.EMAIL_NOT_VERIFIED]: '邮箱未验证',
  [ERROR_CODES.PHONE_NOT_VERIFIED]: '手机号未验证',
  
  [ERROR_CODES.QUESTION_NOT_FOUND]: '问题不存在',
  [ERROR_CODES.ANSWER_NOT_FOUND]: '回答不存在',
  [ERROR_CODES.COMMENT_NOT_FOUND]: '评论不存在',
  [ERROR_CODES.CATEGORY_NOT_FOUND]: '分类不存在',
  [ERROR_CODES.RESOURCE_NOT_FOUND]: '资源不存在',
  
  [ERROR_CODES.FILE_TOO_LARGE]: '文件过大',
  [ERROR_CODES.FILE_TYPE_NOT_ALLOWED]: '文件类型不允许',
  [ERROR_CODES.UPLOAD_FAILED]: '文件上传失败',
  
  [ERROR_CODES.PAYMENT_FAILED]: '支付失败',
  [ERROR_CODES.PAYMENT_CANCELLED]: '支付已取消',
  [ERROR_CODES.PAYMENT_EXPIRED]: '支付已过期',
  [ERROR_CODES.INSUFFICIENT_BALANCE]: '余额不足',
  
  [ERROR_CODES.DATABASE_ERROR]: '数据库错误',
  [ERROR_CODES.CACHE_ERROR]: '缓存错误',
  [ERROR_CODES.EMAIL_SERVICE_ERROR]: '邮件服务错误',
  [ERROR_CODES.SMS_SERVICE_ERROR]: '短信服务错误',
  [ERROR_CODES.EXTERNAL_SERVICE_ERROR]: '外部服务错误'
}

// 创建带错误代码的错误
const createError = (code, customMessage = null, statusCode = 400) => {
  const message = customMessage || ERROR_MESSAGES[code] || '未知错误'
  const error = new ApiError(statusCode, message)
  error.code = code
  return error
}

// 错误工厂函数
const errorFactory = {
  validation: (message, errors = []) => new ValidationError(message, errors),
  authentication: (message) => new AuthenticationError(message),
  authorization: (message) => new AuthorizationError(message),
  notFound: (message) => new NotFoundError(message),
  conflict: (message) => new ConflictError(message),
  rateLimit: (message) => new RateLimitError(message),
  internal: (message) => new InternalServerError(message),
  database: (message) => new DatabaseError(message),
  externalService: (message, service) => new ExternalServiceError(message, service)
}

module.exports = {
  ApiError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  InternalServerError,
  DatabaseError,
  ExternalServiceError,
  ERROR_CODES,
  ERROR_MESSAGES,
  createError,
  errorFactory
}
