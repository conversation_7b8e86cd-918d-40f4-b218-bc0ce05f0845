<template>
  <div class="login-page">
    <div class="login-container">
      <!-- 左侧装饰区域 -->
      <div class="login-decoration">
        <div class="decoration-content">
          <h1 class="decoration-title">
            欢迎回到
            <span class="gradient-text">Materials Studio答疑平台</span>
          </h1>
          <p class="decoration-subtitle">
            专业的分子模拟学习与问答社区
          </p>
          <div class="decoration-features">
            <div class="feature-item">
              <el-icon><Star /></el-icon>
              <span>专业问答</span>
            </div>
            <div class="feature-item">
              <el-icon><VideoPlay /></el-icon>
              <span>视频教程</span>
            </div>
            <div class="feature-item">
              <el-icon><Download /></el-icon>
              <span>资源下载</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧登录表单 -->
      <div class="login-form-section">
        <div class="form-container">
          <div class="form-header">
            <h2 class="form-title">登录账号</h2>
            <p class="form-subtitle">使用您的账号登录平台</p>
          </div>

          <el-form
            ref="loginFormRef"
            :model="loginForm"
            :rules="loginRules"
            class="login-form"
            size="large"
            @submit.prevent="handleLogin"
          >
            <!-- 登录方式切换 -->
            <div class="login-type-tabs">
              <el-button
                :type="loginType === 'password' ? 'primary' : 'default'"
                @click="loginType = 'password'"
                link
              >
                密码登录
              </el-button>
              <el-button
                :type="loginType === 'code' ? 'primary' : 'default'"
                @click="loginType = 'code'"
                link
              >
                验证码登录
              </el-button>
            </div>

            <!-- 用户名/手机号输入 -->
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                :placeholder="loginType === 'code' ? '请输入手机号' : '请输入用户名/邮箱/手机号'"
                :prefix-icon="User"
                clearable
              />
            </el-form-item>

            <!-- 密码登录 -->
            <template v-if="loginType === 'password'">
              <el-form-item prop="password">
                <el-input
                  v-model="loginForm.password"
                  type="password"
                  placeholder="请输入密码"
                  :prefix-icon="Lock"
                  show-password
                  clearable
                />
              </el-form-item>
            </template>

            <!-- 验证码登录 -->
            <template v-else>
              <el-form-item prop="code">
                <div class="code-input-group">
                  <el-input
                    v-model="loginForm.code"
                    placeholder="请输入验证码"
                    :prefix-icon="Message"
                    clearable
                  />
                  <el-button
                    :disabled="codeCountdown > 0"
                    @click="sendCode"
                    :loading="sendingCode"
                  >
                    {{ codeCountdown > 0 ? `${codeCountdown}s` : '获取验证码' }}
                  </el-button>
                </div>
              </el-form-item>
            </template>

            <!-- 记住登录和忘记密码 -->
            <div class="form-options">
              <el-checkbox v-model="loginForm.remember">
                记住登录状态
              </el-checkbox>
              <el-button type="primary" link @click="showForgotPassword">
                忘记密码？
              </el-button>
            </div>

            <!-- 登录按钮 -->
            <el-form-item>
              <el-button
                type="primary"
                class="login-button"
                :loading="userStore.isLoading"
                @click="handleLogin"
                native-type="submit"
              >
                {{ userStore.isLoading ? '登录中...' : '登录' }}
              </el-button>
            </el-form-item>

            <!-- 注册链接 -->
            <div class="register-link">
              <span>还没有账号？</span>
              <router-link to="/register" class="register-btn">
                立即注册
              </router-link>
            </div>
          </el-form>

          <!-- 第三方登录 -->
          <div class="social-login">
            <div class="divider">
              <span>或使用以下方式登录</span>
            </div>
            <div class="social-buttons">
              <el-button circle size="large" @click="handleSocialLogin('wechat')">
                <el-icon><ChatDotRound /></el-icon>
              </el-button>
              <el-button circle size="large" @click="handleSocialLogin('qq')">
                <el-icon><Message /></el-icon>
              </el-button>
              <el-button circle size="large" @click="handleSocialLogin('github')">
                <el-icon><Link /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 忘记密码对话框 -->
    <el-dialog
      v-model="forgotPasswordVisible"
      title="找回密码"
      width="400px"
      center
    >
      <el-form :model="forgotForm" :rules="forgotRules" ref="forgotFormRef">
        <el-form-item prop="email">
          <el-input
            v-model="forgotForm.email"
            placeholder="请输入注册邮箱"
            :prefix-icon="Message"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="forgotPasswordVisible = false">取消</el-button>
        <el-button type="primary" @click="handleForgotPassword" :loading="sendingReset">
          发送重置邮件
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import {
  User,
  Lock,
  Message,
  Star,
  VideoPlay,
  Download,
  ChatDotRound,
  Link
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 表单引用
const loginFormRef = ref()
const forgotFormRef = ref()

// 登录类型
const loginType = ref('password')

// 验证码倒计时
const codeCountdown = ref(0)
const sendingCode = ref(false)

// 忘记密码
const forgotPasswordVisible = ref(false)
const sendingReset = ref(false)

// 登录表单
const loginForm = reactive({
  username: '',
  password: '',
  code: '',
  remember: true
})

// 忘记密码表单
const forgotForm = reactive({
  email: ''
})

// 表单验证规则
const loginRules = reactive({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '验证码格式不正确', trigger: 'blur' }
  ]
})

const forgotRules = reactive({
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '邮箱格式不正确', trigger: 'blur' }
  ]
})

// 发送验证码
const sendCode = async () => {
  if (!loginForm.username) {
    ElMessage.warning('请先输入手机号')
    return
  }

  if (!/^1[3-9]\d{9}$/.test(loginForm.username)) {
    ElMessage.warning('手机号格式不正确')
    return
  }

  try {
    sendingCode.value = true
    const result = await userStore.sendVerificationCode(loginForm.username, 2)
    
    if (result.success) {
      // 开始倒计时
      codeCountdown.value = 60
      const timer = setInterval(() => {
        codeCountdown.value--
        if (codeCountdown.value <= 0) {
          clearInterval(timer)
        }
      }, 1000)
    }
  } catch (error) {
    console.error('Send code error:', error)
  } finally {
    sendingCode.value = false
  }
}

// 处理登录
const handleLogin = async () => {
  try {
    await loginFormRef.value.validate()
    
    const credentials = {
      loginType: loginType.value,
      username: loginForm.username
    }
    
    if (loginType.value === 'password') {
      credentials.password = loginForm.password
    } else {
      credentials.target = loginForm.username
      credentials.code = loginForm.code
    }
    
    const result = await userStore.login(credentials)
    
    if (result.success) {
      // 登录成功，跳转到目标页面
      const redirect = route.query.redirect || '/'
      router.push(redirect)
    }
  } catch (error) {
    console.error('Login error:', error)
  }
}

// 显示忘记密码对话框
const showForgotPassword = () => {
  forgotPasswordVisible.value = true
}

// 处理忘记密码
const handleForgotPassword = async () => {
  try {
    await forgotFormRef.value.validate()
    sendingReset.value = true
    
    // 这里调用忘记密码API
    // await api.post('/users/forgot-password', { email: forgotForm.email })
    
    ElMessage.success('重置邮件已发送，请查收邮箱')
    forgotPasswordVisible.value = false
  } catch (error) {
    console.error('Forgot password error:', error)
  } finally {
    sendingReset.value = false
  }
}

// 第三方登录
const handleSocialLogin = (provider) => {
  ElMessage.info(`${provider}登录功能开发中...`)
}

onMounted(() => {
  // 如果已经登录，直接跳转
  if (userStore.isAuthenticated) {
    const redirect = route.query.redirect || '/'
    router.push(redirect)
  }
})
</script>

<style lang="scss" scoped>
.login-page {
  min-height: 100vh;
  background: var(--bg-gradient);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  max-width: 1000px;
  width: 100%;
  background: var(--bg-card);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  overflow: hidden;
  backdrop-filter: blur(10px);
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    max-width: 400px;
  }
}

// 左侧装饰区域
.login-decoration {
  background: var(--bg-gradient);
  padding: 60px 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-white);
  
  @media (max-width: 768px) {
    display: none;
  }
  
  .decoration-content {
    text-align: center;
  }
  
  .decoration-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 16px;
    line-height: 1.3;
  }
  
  .gradient-text {
    background: linear-gradient(45deg, #f093fb, #f5576c);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .decoration-subtitle {
    font-size: 1.125rem;
    margin-bottom: 40px;
    opacity: 0.9;
  }
  
  .decoration-features {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  
  .feature-item {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1rem;
    
    .el-icon {
      font-size: 20px;
    }
  }
}

// 右侧表单区域
.login-form-section {
  padding: 60px 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  @media (max-width: 768px) {
    padding: 40px 30px;
  }
}

.form-container {
  width: 100%;
  max-width: 360px;
}

.form-header {
  text-align: center;
  margin-bottom: 40px;
  
  .form-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
  }
  
  .form-subtitle {
    color: var(--text-secondary);
  }
}

.login-type-tabs {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-bottom: 32px;
  
  .el-button {
    font-size: 1rem;
    padding: 0;
  }
}

.login-form {
  .code-input-group {
    display: flex;
    gap: 12px;
    
    .el-input {
      flex: 1;
    }
    
    .el-button {
      white-space: nowrap;
    }
  }
  
  .form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
  }
  
  .login-button {
    width: 100%;
    height: 48px;
    font-size: 1rem;
    font-weight: 500;
  }
  
  .register-link {
    text-align: center;
    color: var(--text-secondary);
    
    .register-btn {
      color: var(--primary-color);
      text-decoration: none;
      font-weight: 500;
      margin-left: 8px;
      
      &:hover {
        color: var(--primary-dark);
      }
    }
  }
}

// 第三方登录
.social-login {
  margin-top: 32px;
  
  .divider {
    position: relative;
    text-align: center;
    margin-bottom: 24px;
    
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      height: 1px;
      background: var(--border-color);
    }
    
    span {
      background: var(--bg-card);
      padding: 0 16px;
      color: var(--text-light);
      font-size: 0.875rem;
    }
  }
  
  .social-buttons {
    display: flex;
    justify-content: center;
    gap: 16px;
    
    .el-button {
      border: 1px solid var(--border-color);
      
      &:hover {
        border-color: var(--primary-color);
        color: var(--primary-color);
      }
    }
  }
}

// 表单项样式调整
:deep(.el-form-item) {
  margin-bottom: 24px;
  
  .el-input__wrapper {
    height: 48px;
    border-radius: var(--radius-md);
  }
}

:deep(.el-checkbox) {
  .el-checkbox__label {
    color: var(--text-secondary);
  }
}
</style>
