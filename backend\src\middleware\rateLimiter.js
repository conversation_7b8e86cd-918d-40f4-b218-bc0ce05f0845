const rateLimit = require('express-rate-limit')
const { redisUtils } = require('../config/redis')
const config = require('../config')
const logger = require('../utils/logger')
const { RateLimitError } = require('../utils/errors')

// 创建Redis存储器
class RedisStore {
  constructor(options = {}) {
    this.prefix = options.prefix || 'rate_limit:'
    this.expiry = options.expiry || 60 * 15 // 15分钟
  }

  async increment(key) {
    const fullKey = this.prefix + key
    const current = await redisUtils.incr(fullKey)
    
    if (current === 1) {
      await redisUtils.expire(fullKey, this.expiry)
    }
    
    const ttl = await redisUtils.ttl(fullKey)
    
    return {
      totalHits: current,
      resetTime: new Date(Date.now() + ttl * 1000)
    }
  }

  async decrement(key) {
    const fullKey = this.prefix + key
    const current = await redisUtils.decr(fullKey)
    return Math.max(0, current)
  }

  async resetKey(key) {
    const fullKey = this.prefix + key
    await redisUtils.del(fullKey)
  }
}

// 基础限流器
const createRateLimiter = (options = {}) => {
  const defaultOptions = {
    windowMs: config.rateLimit.windowMs,
    max: config.rateLimit.max,
    message: config.rateLimit.message,
    standardHeaders: true,
    legacyHeaders: false,
    store: new RedisStore({
      prefix: options.prefix || 'rate_limit:',
      expiry: Math.ceil((options.windowMs || config.rateLimit.windowMs) / 1000)
    }),
    keyGenerator: (req) => {
      // 优先使用用户ID，其次使用IP
      return req.user?.id ? `user:${req.user.id}` : `ip:${req.ip}`
    },
    handler: (req, res) => {
      logger.logSecurityEvent('Rate limit exceeded', {
        ip: req.ip,
        userId: req.user?.id,
        path: req.path,
        method: req.method,
        userAgent: req.get('User-Agent')
      })

      const error = new RateLimitError(options.message || config.rateLimit.message)
      res.status(429).json({
        code: 429,
        message: error.message,
        error: {
          type: 'RATE_LIMIT_EXCEEDED',
          timestamp: new Date().toISOString(),
          retryAfter: Math.ceil(options.windowMs / 1000) || 900
        }
      })
    },
    skip: (req) => {
      // 跳过健康检查和静态资源
      return req.path === '/health' || req.path.startsWith('/static/')
    },
    ...options
  }

  return rateLimit(defaultOptions)
}

// 通用API限流器
const rateLimitGeneral = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 每个IP或用户15分钟内最多100个请求
  message: '请求过于频繁，请稍后再试',
  prefix: 'general:'
})

// 认证相关限流器（更严格）
const rateLimitAuth = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 10, // 每个IP15分钟内最多10次认证请求
  message: '认证请求过于频繁，请稍后再试',
  prefix: 'auth:',
  keyGenerator: (req) => `ip:${req.ip}` // 认证请求只按IP限制
})

// 发送验证码限流器
const rateLimitVerificationCode = createRateLimiter({
  windowMs: 60 * 1000, // 1分钟
  max: 1, // 每个目标1分钟内最多1次
  message: '验证码发送过于频繁，请稍后再试',
  prefix: 'verification:',
  keyGenerator: (req) => {
    const target = req.body.target || req.body.email || req.body.phone
    return `target:${target}`
  }
})

// 密码重置限流器
const rateLimitPasswordReset = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 3, // 每个IP1小时内最多3次密码重置请求
  message: '密码重置请求过于频繁，请稍后再试',
  prefix: 'password_reset:',
  keyGenerator: (req) => `ip:${req.ip}`
})

// 文件上传限流器
const rateLimitUpload = createRateLimiter({
  windowMs: 60 * 1000, // 1分钟
  max: 10, // 每个用户1分钟内最多10次上传
  message: '文件上传过于频繁，请稍后再试',
  prefix: 'upload:',
  keyGenerator: (req) => req.user?.id ? `user:${req.user.id}` : `ip:${req.ip}`
})

// 搜索限流器
const rateLimitSearch = createRateLimiter({
  windowMs: 60 * 1000, // 1分钟
  max: 30, // 每个用户1分钟内最多30次搜索
  message: '搜索请求过于频繁，请稍后再试',
  prefix: 'search:',
  keyGenerator: (req) => req.user?.id ? `user:${req.user.id}` : `ip:${req.ip}`
})

// 评论/回答限流器
const rateLimitContent = createRateLimiter({
  windowMs: 60 * 1000, // 1分钟
  max: 5, // 每个用户1分钟内最多5次内容发布
  message: '内容发布过于频繁，请稍后再试',
  prefix: 'content:',
  keyGenerator: (req) => req.user?.id ? `user:${req.user.id}` : `ip:${req.ip}`
})

// 点赞/收藏限流器
const rateLimitInteraction = createRateLimiter({
  windowMs: 10 * 1000, // 10秒
  max: 20, // 每个用户10秒内最多20次互动
  message: '操作过于频繁，请稍后再试',
  prefix: 'interaction:',
  keyGenerator: (req) => req.user?.id ? `user:${req.user.id}` : `ip:${req.ip}`
})

// 管理员操作限流器（相对宽松）
const rateLimitAdmin = createRateLimiter({
  windowMs: 60 * 1000, // 1分钟
  max: 100, // 每个管理员1分钟内最多100次操作
  message: '管理操作过于频繁，请稍后再试',
  prefix: 'admin:',
  keyGenerator: (req) => `admin:${req.user.id}`
})

// 动态限流器 - 根据用户等级调整限制
const createDynamicRateLimiter = (baseOptions) => {
  return (req, res, next) => {
    let maxRequests = baseOptions.max

    // 根据用户会员等级调整限制
    if (req.user) {
      switch (req.user.memberLevel) {
        case 1: // 初级会员
          maxRequests = Math.floor(maxRequests * 1.5)
          break
        case 2: // 高级会员
          maxRequests = Math.floor(maxRequests * 2)
          break
        default: // 普通用户
          break
      }

      // 管理员和专家用户更宽松的限制
      if (req.user.role === 'admin' || req.user.role === 'expert') {
        maxRequests = Math.floor(maxRequests * 3)
      }
    }

    const limiter = createRateLimiter({
      ...baseOptions,
      max: maxRequests
    })

    limiter(req, res, next)
  }
}

// 智能限流器 - 根据请求模式动态调整
const createSmartRateLimiter = (options) => {
  return async (req, res, next) => {
    const key = options.keyGenerator ? options.keyGenerator(req) : `ip:${req.ip}`
    const patternKey = `pattern:${key}`
    
    // 获取历史请求模式
    const recentRequests = await redisUtils.lrange(patternKey, 0, 9) // 最近10次请求
    
    // 分析请求模式
    let suspiciousActivity = false
    if (recentRequests.length >= 5) {
      const intervals = []
      for (let i = 1; i < recentRequests.length; i++) {
        intervals.push(recentRequests[i].timestamp - recentRequests[i-1].timestamp)
      }
      
      // 如果请求间隔过于规律（可能是机器人）
      const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length
      const variance = intervals.reduce((sum, interval) => sum + Math.pow(interval - avgInterval, 2), 0) / intervals.length
      
      if (variance < 100 && avgInterval < 1000) { // 间隔小于1秒且很规律
        suspiciousActivity = true
      }
    }
    
    // 记录当前请求
    await redisUtils.lpush(patternKey, {
      timestamp: Date.now(),
      path: req.path,
      method: req.method
    })
    await redisUtils.expire(patternKey, 300) // 5分钟过期
    
    // 如果检测到可疑活动，应用更严格的限制
    const maxRequests = suspiciousActivity ? Math.floor(options.max * 0.3) : options.max
    
    const limiter = createRateLimiter({
      ...options,
      max: maxRequests
    })
    
    limiter(req, res, next)
  }
}

module.exports = {
  rateLimitGeneral,
  rateLimitAuth,
  rateLimitVerificationCode,
  rateLimitPasswordReset,
  rateLimitUpload,
  rateLimitSearch,
  rateLimitContent,
  rateLimitInteraction,
  rateLimitAdmin,
  createRateLimiter,
  createDynamicRateLimiter,
  createSmartRateLimiter,
  RedisStore
}
