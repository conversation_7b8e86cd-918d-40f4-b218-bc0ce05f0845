import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/api'
import { ElMessage } from 'element-plus'
import Cookies from 'js-cookie'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref(null)
  const token = ref(Cookies.get('token') || null)
  const isLoading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'admin')
  const isMember = computed(() => user.value?.memberLevel > 0)
  const memberLevel = computed(() => user.value?.memberLevel || 0)

  // 方法
  const setToken = (newToken) => {
    token.value = newToken
    if (newToken) {
      Cookies.set('token', newToken, { expires: 7 })
      api.defaults.headers.common['Authorization'] = `Bearer ${newToken}`
    } else {
      Cookies.remove('token')
      delete api.defaults.headers.common['Authorization']
    }
  }

  const setUser = (userData) => {
    user.value = userData
  }

  const login = async (credentials) => {
    try {
      isLoading.value = true
      const response = await api.post('/users/login', credentials)
      const { token: newToken, user: userData } = response.data.data
      
      setToken(newToken)
      setUser(userData)
      
      ElMessage.success('登录成功')
      return { success: true }
    } catch (error) {
      const message = error.response?.data?.message || '登录失败'
      ElMessage.error(message)
      return { success: false, message }
    } finally {
      isLoading.value = false
    }
  }

  const register = async (userData) => {
    try {
      isLoading.value = true
      const response = await api.post('/users/register', userData)
      
      ElMessage.success('注册成功，请登录')
      return { success: true }
    } catch (error) {
      const message = error.response?.data?.message || '注册失败'
      ElMessage.error(message)
      return { success: false, message }
    } finally {
      isLoading.value = false
    }
  }

  const logout = async () => {
    try {
      await api.post('/users/logout')
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      setToken(null)
      setUser(null)
      ElMessage.success('已退出登录')
    }
  }

  const fetchUserProfile = async () => {
    try {
      const response = await api.get('/users/profile')
      setUser(response.data.data)
      return response.data.data
    } catch (error) {
      console.error('Fetch profile error:', error)
      if (error.response?.status === 401) {
        logout()
      }
      throw error
    }
  }

  const updateProfile = async (profileData) => {
    try {
      isLoading.value = true
      const response = await api.put('/users/profile', profileData)
      setUser(response.data.data)
      ElMessage.success('个人资料更新成功')
      return { success: true }
    } catch (error) {
      const message = error.response?.data?.message || '更新失败'
      ElMessage.error(message)
      return { success: false, message }
    } finally {
      isLoading.value = false
    }
  }

  const sendVerificationCode = async (target, type) => {
    try {
      await api.post('/users/verification-code', { target, type })
      ElMessage.success('验证码已发送')
      return { success: true }
    } catch (error) {
      const message = error.response?.data?.message || '发送失败'
      ElMessage.error(message)
      return { success: false, message }
    }
  }

  const initializeAuth = async () => {
    if (token.value) {
      api.defaults.headers.common['Authorization'] = `Bearer ${token.value}`
      try {
        await fetchUserProfile()
      } catch (error) {
        console.error('Initialize auth error:', error)
        logout()
      }
    }
  }

  const refreshToken = async () => {
    try {
      const response = await api.post('/users/refresh-token')
      const { token: newToken } = response.data.data
      setToken(newToken)
      return newToken
    } catch (error) {
      logout()
      throw error
    }
  }

  return {
    // 状态
    user: readonly(user),
    token: readonly(token),
    isLoading: readonly(isLoading),
    
    // 计算属性
    isAuthenticated,
    isAdmin,
    isMember,
    memberLevel,
    
    // 方法
    login,
    register,
    logout,
    fetchUserProfile,
    updateProfile,
    sendVerificationCode,
    initializeAuth,
    refreshToken,
    setToken,
    setUser
  }
})
