<template>
  <div class="notification-list">
    <!-- 通知头部 -->
    <div class="notification-header">
      <h3>消息通知</h3>
      <div class="header-actions">
        <el-button 
          type="primary" 
          link 
          size="small"
          @click="markAllAsRead"
          v-if="unreadCount > 0"
        >
          全部已读
        </el-button>
        <el-button 
          type="danger" 
          link 
          size="small"
          @click="clearAll"
        >
          清空
        </el-button>
      </div>
    </div>

    <!-- 通知筛选 -->
    <div class="notification-filter">
      <el-radio-group v-model="activeFilter" size="small">
        <el-radio-button label="all">全部</el-radio-button>
        <el-radio-button label="unread">未读</el-radio-button>
        <el-radio-button label="system">系统</el-radio-button>
        <el-radio-button label="interaction">互动</el-radio-button>
      </el-radio-group>
    </div>

    <!-- 通知列表 -->
    <div class="notification-content" v-loading="isLoading">
      <div 
        v-if="filteredNotifications.length === 0" 
        class="empty-state"
      >
        <el-empty 
          :image-size="80"
          description="暂无通知"
        />
      </div>
      
      <div 
        v-else
        class="notification-items"
      >
        <div
          v-for="notification in filteredNotifications"
          :key="notification.id"
          class="notification-item"
          :class="{ 'unread': !notification.isRead }"
          @click="handleNotificationClick(notification)"
        >
          <!-- 通知图标 -->
          <div class="notification-icon">
            <el-icon :class="getNotificationIconClass(notification.type)">
              <component :is="getNotificationIcon(notification.type)" />
            </el-icon>
          </div>

          <!-- 通知内容 -->
          <div class="notification-body">
            <div class="notification-title">
              {{ notification.title }}
            </div>
            <div class="notification-message">
              {{ notification.content }}
            </div>
            <div class="notification-time">
              {{ formatTime(notification.createdAt) }}
            </div>
          </div>

          <!-- 未读标识 -->
          <div v-if="!notification.isRead" class="unread-dot"></div>

          <!-- 操作按钮 -->
          <div class="notification-actions">
            <el-button
              type="primary"
              link
              size="small"
              @click.stop="markAsRead(notification)"
              v-if="!notification.isRead"
            >
              标记已读
            </el-button>
            <el-button
              type="danger"
              link
              size="small"
              @click.stop="deleteNotification(notification)"
            >
              删除
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载更多 -->
    <div class="notification-footer" v-if="hasMore">
      <el-button 
        @click="loadMore" 
        :loading="isLoadingMore"
        text
      >
        加载更多
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'
import {
  Bell,
  Message,
  Star,
  User,
  Warning,
  Success,
  Info,
  QuestionFilled
} from '@element-plus/icons-vue'

dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

const emit = defineEmits(['close'])

// 响应式数据
const notifications = ref([])
const isLoading = ref(false)
const isLoadingMore = ref(false)
const activeFilter = ref('all')
const hasMore = ref(true)
const page = ref(1)

// 计算属性
const unreadCount = computed(() => {
  return notifications.value.filter(n => !n.isRead).length
})

const filteredNotifications = computed(() => {
  let filtered = notifications.value

  switch (activeFilter.value) {
    case 'unread':
      filtered = filtered.filter(n => !n.isRead)
      break
    case 'system':
      filtered = filtered.filter(n => n.type.startsWith('system'))
      break
    case 'interaction':
      filtered = filtered.filter(n => ['like', 'comment', 'answer', 'mention'].includes(n.type))
      break
  }

  return filtered
})

// 获取通知图标
const getNotificationIcon = (type) => {
  const iconMap = {
    'system': Bell,
    'like': Star,
    'comment': Message,
    'answer': QuestionFilled,
    'mention': User,
    'warning': Warning,
    'success': Success,
    'info': Info
  }
  
  return iconMap[type] || Bell
}

// 获取通知图标样式类
const getNotificationIconClass = (type) => {
  const classMap = {
    'system': 'icon-system',
    'like': 'icon-like',
    'comment': 'icon-comment',
    'answer': 'icon-answer',
    'mention': 'icon-mention',
    'warning': 'icon-warning',
    'success': 'icon-success',
    'info': 'icon-info'
  }
  
  return classMap[type] || 'icon-default'
}

// 格式化时间
const formatTime = (time) => {
  return dayjs(time).fromNow()
}

// 获取通知列表
const fetchNotifications = async (reset = false) => {
  try {
    if (reset) {
      isLoading.value = true
      page.value = 1
    } else {
      isLoadingMore.value = true
    }

    // 模拟API调用
    const mockNotifications = [
      {
        id: 1,
        type: 'like',
        title: '收到新的点赞',
        content: '用户"张三"赞了您的回答',
        isRead: false,
        createdAt: new Date(Date.now() - 1000 * 60 * 5), // 5分钟前
        data: { questionId: 123, userId: 456 }
      },
      {
        id: 2,
        type: 'comment',
        title: '收到新的评论',
        content: '用户"李四"评论了您的问题',
        isRead: false,
        createdAt: new Date(Date.now() - 1000 * 60 * 30), // 30分钟前
        data: { questionId: 124, userId: 457 }
      },
      {
        id: 3,
        type: 'answer',
        title: '问题有新回答',
        content: '您关注的问题"Materials Studio如何建模"有新回答',
        isRead: true,
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2小时前
        data: { questionId: 125, answerId: 789 }
      },
      {
        id: 4,
        type: 'system',
        title: '系统通知',
        content: '您的会员即将到期，请及时续费',
        isRead: false,
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1天前
        data: { membershipId: 101 }
      }
    ]

    if (reset) {
      notifications.value = mockNotifications
    } else {
      notifications.value.push(...mockNotifications)
    }

    hasMore.value = page.value < 3 // 模拟分页
  } catch (error) {
    ElMessage.error('获取通知失败')
  } finally {
    isLoading.value = false
    isLoadingMore.value = false
  }
}

// 加载更多
const loadMore = async () => {
  page.value++
  await fetchNotifications()
}

// 处理通知点击
const handleNotificationClick = (notification) => {
  // 标记为已读
  if (!notification.isRead) {
    markAsRead(notification)
  }

  // 根据通知类型跳转到相应页面
  const { type, data } = notification
  
  switch (type) {
    case 'like':
    case 'comment':
    case 'answer':
      if (data.questionId) {
        // 跳转到问题详情页
        window.open(`/questions/${data.questionId}`, '_blank')
      }
      break
    case 'mention':
      if (data.questionId) {
        window.open(`/questions/${data.questionId}`, '_blank')
      }
      break
    case 'system':
      if (data.membershipId) {
        window.open('/membership', '_blank')
      }
      break
  }
}

// 标记为已读
const markAsRead = async (notification) => {
  try {
    // 模拟API调用
    notification.isRead = true
    ElMessage.success('已标记为已读')
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

// 全部标记为已读
const markAllAsRead = async () => {
  try {
    notifications.value.forEach(n => {
      n.isRead = true
    })
    ElMessage.success('全部已标记为已读')
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

// 删除通知
const deleteNotification = async (notification) => {
  try {
    await ElMessageBox.confirm('确定要删除这条通知吗？', '确认删除', {
      type: 'warning'
    })
    
    const index = notifications.value.findIndex(n => n.id === notification.id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
    
    ElMessage.success('删除成功')
  } catch (error) {
    // 用户取消删除
  }
}

// 清空所有通知
const clearAll = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有通知吗？', '确认清空', {
      type: 'warning'
    })
    
    notifications.value = []
    ElMessage.success('清空成功')
  } catch (error) {
    // 用户取消清空
  }
}

onMounted(() => {
  fetchNotifications(true)
})
</script>

<style lang="scss" scoped>
.notification-list {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-light);
  
  h3 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
  }
  
  .header-actions {
    display: flex;
    gap: 8px;
  }
}

.notification-filter {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-light);
}

.notification-content {
  flex: 1;
  overflow-y: auto;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
}

.notification-items {
  padding: 8px 0;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 16px 20px;
  cursor: pointer;
  transition: background-color var(--transition-fast);
  position: relative;
  
  &:hover {
    background-color: var(--bg-secondary);
  }
  
  &.unread {
    background-color: rgba(102, 126, 234, 0.05);
    
    .notification-title {
      font-weight: 600;
    }
  }
}

.notification-icon {
  margin-right: 12px;
  margin-top: 2px;
  
  .el-icon {
    font-size: 20px;
    
    &.icon-like {
      color: var(--warning-color);
    }
    
    &.icon-comment {
      color: var(--info-color);
    }
    
    &.icon-answer {
      color: var(--success-color);
    }
    
    &.icon-mention {
      color: var(--primary-color);
    }
    
    &.icon-warning {
      color: var(--error-color);
    }
    
    &.icon-success {
      color: var(--success-color);
    }
    
    &.icon-system {
      color: var(--text-secondary);
    }
    
    &.icon-default {
      color: var(--text-light);
    }
  }
}

.notification-body {
  flex: 1;
  min-width: 0;
  
  .notification-title {
    font-size: 0.875rem;
    color: var(--text-primary);
    margin-bottom: 4px;
    line-height: 1.4;
  }
  
  .notification-message {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-bottom: 8px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .notification-time {
    font-size: 0.75rem;
    color: var(--text-light);
  }
}

.unread-dot {
  width: 8px;
  height: 8px;
  background-color: var(--primary-color);
  border-radius: 50%;
  margin-left: 8px;
  margin-top: 6px;
  flex-shrink: 0;
}

.notification-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-left: 8px;
  opacity: 0;
  transition: opacity var(--transition-fast);
  
  .notification-item:hover & {
    opacity: 1;
  }
}

.notification-footer {
  padding: 16px 20px;
  text-align: center;
  border-top: 1px solid var(--border-light);
}

// 滚动条样式
.notification-content::-webkit-scrollbar {
  width: 4px;
}

.notification-content::-webkit-scrollbar-track {
  background: transparent;
}

.notification-content::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 2px;
  
  &:hover {
    background: var(--text-light);
  }
}
</style>
